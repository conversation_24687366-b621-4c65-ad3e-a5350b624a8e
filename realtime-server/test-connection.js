#!/usr/bin/env node

/**
 * Simple connection test for VoiceHype Real-time Server
 * Bismillahir rahmanir raheem
 */

import WebSocket from 'ws';

// Configuration
const SERVER_URL = process.argv[2] || 'ws://localhost:3001/realtime';
const API_KEY = process.argv[3] || 'test-api-key';

console.log('🧪 Testing VoiceHype Real-time Server Connection');
console.log('📡 Bismillahir rahmanir raheem');
console.log(`🔗 Server: ${SERVER_URL}`);
console.log(`🔑 API Key: ${API_KEY.substring(0, 8)}...`);
console.log('');

// Create WebSocket connection
const ws = new WebSocket(`${SERVER_URL}?apiKey=${API_KEY}&service=assemblyai&model=best`);

let connected = false;
let sessionId = null;

// Connection opened
ws.on('open', () => {
    console.log('✅ WebSocket connection opened');
    connected = true;
    
    // Send a ping after 2 seconds
    setTimeout(() => {
        if (connected) {
            console.log('🏓 Sending ping...');
            ws.send(JSON.stringify({ type: 'ping' }));
        }
    }, 2000);
    
    // Close connection after 10 seconds
    setTimeout(() => {
        if (connected) {
            console.log('👋 Closing connection...');
            ws.send(JSON.stringify({ type: 'close' }));
        }
    }, 10000);
});

// Message received
ws.on('message', (data) => {
    try {
        const message = JSON.parse(data.toString());
        
        switch (message.type) {
            case 'connected':
                sessionId = message.sessionId;
                console.log(`🎬 Session established: ${sessionId}`);
                console.log(`⏱️  Max duration: ${(message.maxDurationMs / 60000).toFixed(1)} minutes`);
                break;
                
            case 'status':
                console.log(`ℹ️  Status: ${message.message}`);
                break;
                
            case 'pong':
                console.log('🏓 Pong received');
                break;
                
            case 'error':
                console.log(`❌ Error: ${message.message}`);
                break;
                
            case 'finalized':
                console.log(`✅ Session finalized: ${message.message}`);
                break;
                
            default:
                console.log(`📨 Message: ${message.type}`, message);
        }
    } catch (error) {
        console.log('📨 Raw message:', data.toString());
    }
});

// Connection error
ws.on('error', (error) => {
    console.log(`❌ WebSocket error: ${error.message}`);
});

// Connection closed
ws.on('close', (code, reason) => {
    connected = false;
    console.log(`🔌 Connection closed: ${code} ${reason.toString()}`);
    
    if (code === 1000) {
        console.log('✅ Test completed successfully!');
    } else {
        console.log('⚠️  Connection closed unexpectedly');
    }
    
    process.exit(code === 1000 ? 0 : 1);
});

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n📴 Terminating test...');
    if (connected) {
        ws.close();
    } else {
        process.exit(0);
    }
});

console.log('⏳ Connecting... (Press Ctrl+C to cancel)');
