#!/bin/bash

# VoiceHype Real-time Server Deployment Script
# Bismillahir rahmanir raheem

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DROPLET_IP=${1:-""}
DROPLET_USER="root"
REMOTE_DIR="/opt/voicehype/realtime-server"
SERVICE_NAME="voicehype-realtime"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if droplet IP is provided
if [ -z "$DROPLET_IP" ]; then
    print_error "Usage: $0 <droplet-ip>"
    print_error "Example: $0 ***************"
    exit 1
fi

print_status "🚀 Starting deployment to $DROPLET_IP"
print_status "📡 Bismillahir rahmanir raheem"

# Check if we can connect to the droplet
print_status "🔍 Testing connection to droplet..."
if ! ssh -o ConnectTimeout=10 -o BatchMode=yes $DROPLET_USER@$DROPLET_IP exit 2>/dev/null; then
    print_error "Cannot connect to $DROPLET_IP. Please check:"
    print_error "  1. Droplet IP is correct"
    print_error "  2. SSH key is configured"
    print_error "  3. Droplet is running"
    exit 1
fi
print_success "✅ Connection successful"

# Create remote directory
print_status "📁 Creating remote directory..."
ssh $DROPLET_USER@$DROPLET_IP "mkdir -p $REMOTE_DIR"

# Copy files to droplet
print_status "📤 Uploading files..."
rsync -avz --progress \
    --exclude 'node_modules' \
    --exclude '.git' \
    --exclude '*.log' \
    ./ $DROPLET_USER@$DROPLET_IP:$REMOTE_DIR/

print_success "✅ Files uploaded"

# Install dependencies and setup
print_status "📦 Installing dependencies..."
ssh $DROPLET_USER@$DROPLET_IP << 'EOF'
cd /opt/voicehype/realtime-server

# Install Node.js if not present
if ! command -v node &> /dev/null; then
    echo "Installing Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    apt-get install -y nodejs
fi

# Install PM2 if not present
if ! command -v pm2 &> /dev/null; then
    echo "Installing PM2..."
    npm install -g pm2
fi

# Install dependencies
echo "Installing project dependencies..."
npm install --production

# Setup environment file if it doesn't exist
if [ ! -f .env ]; then
    echo "Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your configuration!"
fi
EOF

print_success "✅ Dependencies installed"

# Deploy with PM2
print_status "🔄 Deploying with PM2..."
ssh $DROPLET_USER@$DROPLET_IP << EOF
cd $REMOTE_DIR

# Stop existing process if running
pm2 stop $SERVICE_NAME 2>/dev/null || true
pm2 delete $SERVICE_NAME 2>/dev/null || true

# Start new process
pm2 start server.js --name "$SERVICE_NAME" --log-date-format="YYYY-MM-DD HH:mm:ss Z"

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup systemd -u $DROPLET_USER --hp /root 2>/dev/null || true

# Show status
pm2 status
EOF

print_success "✅ Service deployed with PM2"

# Test the deployment
print_status "🧪 Testing deployment..."
sleep 3

# Check if service is running
if ssh $DROPLET_USER@$DROPLET_IP "pm2 list | grep -q $SERVICE_NAME.*online"; then
    print_success "✅ Service is running"
    
    # Test health endpoint
    if ssh $DROPLET_USER@$DROPLET_IP "curl -s http://localhost:3001/health | grep -q healthy"; then
        print_success "✅ Health check passed"
    else
        print_warning "⚠️  Health check failed - service may still be starting"
    fi
else
    print_error "❌ Service is not running"
    print_status "📋 Checking logs..."
    ssh $DROPLET_USER@$DROPLET_IP "pm2 logs $SERVICE_NAME --lines 10"
    exit 1
fi

# Show final status
print_status "📊 Final status:"
ssh $DROPLET_USER@$DROPLET_IP << EOF
echo "PM2 Status:"
pm2 status

echo ""
echo "Service Logs (last 5 lines):"
pm2 logs $SERVICE_NAME --lines 5 --nostream

echo ""
echo "Server Info:"
echo "  🌐 WebSocket: ws://$DROPLET_IP:3001/realtime"
echo "  🏥 Health: http://$DROPLET_IP:3001/health"
echo "  📋 Logs: pm2 logs $SERVICE_NAME"
echo "  🔄 Restart: pm2 restart $SERVICE_NAME"
echo "  🛑 Stop: pm2 stop $SERVICE_NAME"
EOF

print_success "🎉 Deployment completed successfully!"
print_status "📝 Next steps:"
print_status "  1. Edit .env file: ssh $DROPLET_USER@$DROPLET_IP 'nano $REMOTE_DIR/.env'"
print_status "  2. Restart service: ssh $DROPLET_USER@$DROPLET_IP 'pm2 restart $SERVICE_NAME'"
print_status "  3. Test connection: Update your client to use ws://$DROPLET_IP:3001/realtime"

print_status "🤲 May Allah bless this deployment!"
