<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VoiceHype Real-time Transcription Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        input, button {
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
        }
        
        input {
            flex: 1;
            min-width: 200px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            placeholder-color: rgba(255, 255, 255, 0.7);
        }
        
        input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        button {
            background: #4CAF50;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        
        button:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        
        .status {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: bold;
            text-align: center;
        }
        
        .status.connected {
            background: rgba(76, 175, 80, 0.3);
            border: 2px solid #4CAF50;
        }
        
        .status.disconnected {
            background: rgba(244, 67, 54, 0.3);
            border: 2px solid #f44336;
        }
        
        .transcript-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 25px;
            min-height: 200px;
            margin-bottom: 20px;
        }
        
        .partial-transcript {
            color: #FFD700;
            font-style: italic;
            opacity: 0.8;
            margin-bottom: 10px;
            padding: 10px;
            background: rgba(255, 215, 0, 0.1);
            border-radius: 8px;
            border-left: 4px solid #FFD700;
        }
        
        .final-transcript {
            color: #90EE90;
            font-weight: bold;
            margin-bottom: 15px;
            padding: 12px;
            background: rgba(144, 238, 144, 0.1);
            border-radius: 8px;
            border-left: 4px solid #90EE90;
        }
        
        .logs {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            opacity: 0.8;
        }
        
        .bismillah {
            text-align: center;
            font-style: italic;
            opacity: 0.7;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="bismillah">بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيم</div>
        
        <h1>🎙️ VoiceHype Real-time Transcription</h1>
        
        <div class="controls">
            <input type="text" id="serverUrl" placeholder="Server URL (e.g., ***************:3001)" value="***************:3001">
            <input type="text" id="apiKey" placeholder="Your VoiceHype API Key">
            <button id="connectBtn" onclick="toggleConnection()">Connect</button>
            <button id="recordBtn" onclick="toggleRecording()" disabled>Start Recording</button>
        </div>
        
        <div id="status" class="status disconnected">
            Disconnected - Enter server URL and API key to connect
        </div>
        
        <div class="transcript-container">
            <h3>📝 Live Transcript</h3>
            <div id="partialTranscript" class="partial-transcript" style="display: none;">
                <strong>🔄 Partial:</strong> <span id="partialText"></span>
            </div>
            <div id="finalTranscripts"></div>
        </div>
        
        <div class="logs">
            <h4>📋 Connection Logs</h4>
            <div id="logContainer"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let mediaRecorder = null;
        let audioStream = null;
        let isRecording = false;
        let isConnected = false;

        function log(message) {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateStatus(message, connected = false) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${connected ? 'connected' : 'disconnected'}`;
        }

        function toggleConnection() {
            if (isConnected) {
                disconnect();
            } else {
                connect();
            }
        }

        function connect() {
            const serverUrl = document.getElementById('serverUrl').value;
            const apiKey = document.getElementById('apiKey').value;

            if (!serverUrl || !apiKey) {
                alert('Please enter both server URL and API key');
                return;
            }

            const wsUrl = `ws://${serverUrl}/realtime?apiKey=${apiKey}&service=assemblyai&model=best`;
            
            log(`Connecting to ${wsUrl}`);
            updateStatus('Connecting...', false);

            ws = new WebSocket(wsUrl);

            ws.onopen = () => {
                log('WebSocket connected');
                updateStatus('Connected - Ready to record', true);
                isConnected = true;
                document.getElementById('connectBtn').textContent = 'Disconnect';
                document.getElementById('recordBtn').disabled = false;
            };

            ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    handleMessage(data);
                } catch (error) {
                    log(`Error parsing message: ${error.message}`);
                }
            };

            ws.onclose = (event) => {
                log(`WebSocket closed: ${event.code} ${event.reason}`);
                updateStatus('Disconnected', false);
                isConnected = false;
                document.getElementById('connectBtn').textContent = 'Connect';
                document.getElementById('recordBtn').disabled = true;
                if (isRecording) {
                    stopRecording();
                }
            };

            ws.onerror = (error) => {
                log(`WebSocket error: ${error.message || 'Unknown error'}`);
                updateStatus('Connection error', false);
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        function handleMessage(data) {
            const messageType = data.message_type || data.type;
            
            switch (messageType) {
                case 'connected':
                    log(`Session established: ${data.sessionId}`);
                    updateStatus(`Connected - Session: ${data.sessionId}`, true);
                    break;
                    
                case 'PartialTranscript':
                    if (data.text && data.text.trim()) {
                        showPartialTranscript(data.text);
                        log(`Partial: "${data.text}"`);
                    }
                    break;
                    
                case 'FinalTranscript':
                    if (data.text && data.text.trim()) {
                        addFinalTranscript(data.text);
                        hidePartialTranscript();
                        log(`Final: "${data.text}"`);
                    }
                    break;
                    
                case 'status':
                    log(`Status: ${data.message}`);
                    break;
                    
                case 'error':
                    log(`Error: ${data.message}`);
                    updateStatus(`Error: ${data.message}`, false);
                    break;
                    
                case 'finalized':
                    log('Session finalized');
                    break;
                    
                default:
                    log(`Unknown message: ${messageType}`);
            }
        }

        function showPartialTranscript(text) {
            const partialDiv = document.getElementById('partialTranscript');
            const partialText = document.getElementById('partialText');
            partialText.textContent = text;
            partialDiv.style.display = 'block';
        }

        function hidePartialTranscript() {
            const partialDiv = document.getElementById('partialTranscript');
            partialDiv.style.display = 'none';
        }

        function addFinalTranscript(text) {
            const finalTranscripts = document.getElementById('finalTranscripts');
            const transcriptDiv = document.createElement('div');
            transcriptDiv.className = 'final-transcript';
            transcriptDiv.innerHTML = `<strong>✅ Final:</strong> ${text}`;
            finalTranscripts.appendChild(transcriptDiv);
            finalTranscripts.scrollTop = finalTranscripts.scrollHeight;
        }

        async function toggleRecording() {
            if (isRecording) {
                stopRecording();
            } else {
                await startRecording();
            }
        }

        async function startRecording() {
            try {
                audioStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    }
                });

                // Note: This is a simplified example. For production, you'd need to:
                // 1. Convert audio to PCM16 format
                // 2. Resample to 16kHz if needed
                // 3. Send binary audio data to WebSocket
                
                log('Recording started (Note: Audio processing not implemented in this demo)');
                isRecording = true;
                document.getElementById('recordBtn').textContent = 'Stop Recording';
                updateStatus('Recording... Speak now!', true);
                
            } catch (error) {
                log(`Error starting recording: ${error.message}`);
                alert('Error accessing microphone. Please check permissions.');
            }
        }

        function stopRecording() {
            if (audioStream) {
                audioStream.getTracks().forEach(track => track.stop());
                audioStream = null;
            }
            
            isRecording = false;
            document.getElementById('recordBtn').textContent = 'Start Recording';
            updateStatus('Connected - Ready to record', true);
            log('Recording stopped');
        }

        // Initialize
        log('VoiceHype Real-time Transcription Demo loaded');
        log('Note: This demo shows the WebSocket connection and message handling.');
        log('Audio processing requires additional implementation for production use.');
    </script>
</body>
</html>
