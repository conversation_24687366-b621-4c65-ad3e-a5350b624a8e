{"name": "voicehype-realtime-server", "version": "1.0.0", "description": "VoiceHype Real-time Transcription Server", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "ws": "^8.14.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "@supabase/supabase-js": "^2.38.0", "node-fetch": "^3.3.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "type": "module", "keywords": ["websocket", "transcription", "assemblyai", "voicehype"], "author": "VoiceHype", "license": "ISC"}