{"name": "voicehype", "displayName": "VoiceHype", "description": "Voice-to-prompt productivity tool for developers. Transcribe speech using AssemblyAI or Whisper models, with real-time capabilities and AI optimization. Control recording with keyboard shortcuts (Ctrl+Shift+8/9/0).", "version": "1.0.8", "publisher": "VoiceHype", "icon": "assets/tv_logo.png", "repository": {"type": "git", "url": "git+https://github.com/voicehype/voicehype.git"}, "engines": {"vscode": "^1.93.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./dist/index.js", "contributes": {"configuration": {"title": "VoiceHype", "properties": {"voicehype.audio.device": {"type": "string", "default": "", "description": "Audio input device to use for recording (e.g., 'pulse', 'alsa', 'hw:1,0' etc.). Leave empty to use system default."}, "voicehype.audio.sampleRate": {"type": "number", "default": 16000, "description": "Sample rate for audio recording in Hz", "enum": [8000, 16000, 22050, 44100, 48000], "enumDescriptions": ["8kHz - Low quality, smaller file size", "16kHz - Good for speech recognition (recommended for real-time)", "22.05kHz - Good quality, balanced file size", "44.1kHz - CD quality", "48kHz - Professional audio quality"]}, "voicehype.transcription.language": {"type": "string", "default": "en", "description": "Language of the audio for transcription. Available languages depend on the selected service and model. For AssemblyAI 'best' model, only major languages are supported. For AssemblyAI 'nano' model and Whisper, a wider range of languages is available."}, "voicehype.transcription.model": {"type": "string", "default": "best", "description": "Model to use for transcription (model options depend on selected service)", "enum": ["best", "nano", "whisper-1", "gpt-4o-mini-transcribe", "gpt-4o-transcribe"], "enumDescriptions": ["AssemblyAI", "AssemblyAI", "OpenAI", "OpenAI", "OpenAI"]}, "voicehype.transcription.service": {"type": "string", "default": "assemblyai", "description": "Transcription service to use", "enum": ["assemblyai", "openai"], "enumDescriptions": ["AssemblyAI", "OpenAI"]}, "voicehype.transcription.optimizationModel": {"type": "string", "default": "gpt-4o", "description": "AI model to use for optimizing transcripts", "enum": ["gpt-4o", "gpt-4o-mini", "o3-mini", "llama-4-scout", "llama-4-maverick", "llama-3-70b", "llama-3-8b", "claude-3.5-sonnet", "claude-3.7-sonnet", "claude-3.5-haiku", "deepseek-r1", "deepseek-v3"], "enumDescriptions": ["GPT-4o - Latest OpenAI model", "GPT-4o Mini - Mini version of GPT-4o", "O3 Mini - Extremely fast reasoning model", "Llama 4 Scout - Meta's latest large language model", "Llama 4 Maverick - Meta's latest large language model", "Llama 3 70B - Meta's 70B parameter model", "Llama 3 8B - Meta's 8B parameter model", "Claude 3.5 Sonnet - <PERSON><PERSON><PERSON>'s balanced model", "Claude 3.7 Sonnet - Anthrop<PERSON>'s latest model", "Claude 3.5 Hai<PERSON> - <PERSON><PERSON><PERSON>'s fast model", "DeepSeek R1 - DeepSeek's reasoning model", "DeepSeek V3 - DeepSeek's latest model"]}, "voicehype.transcription.shouldOptimize": {"type": "boolean", "default": false, "description": "Whether to optimize transcripts with AI by default"}, "voicehype.transcription.translate": {"type": "boolean", "default": false, "description": "Whether to translate audio to English instead of transcribing in the original language"}, "voicehype.transcription.realtime": {"type": "boolean", "default": false, "description": "Whether to use real-time transcription (only available with certain models)"}, "voicehype.transcription.customPrompt": {"type": "string", "default": "", "description": "Custom prompt for AI optimization of transcripts. You can use {{transcript}} as a variable that will be replaced with the actual transcript text."}, "voicehype.apiKey": {"type": "string", "default": "", "description": "API key for VoiceHype services. You can create one at voicehype.ai"}, "voicehype.promptModes.custom": {"type": "array", "default": [], "description": "Custom prompt modes for transcription optimization"}, "voicehype.promptModes.active": {"type": "string", "default": "clean-up", "description": "Currently active prompt mode for transcription optimization"}}}, "viewsContainers": {"activitybar": [{"id": "voicehype-panel", "title": "VoiceHype", "icon": "assets/tv_icon.svg"}]}, "views": {"voicehype-panel": [{"id": "voicehype.controlPanel", "name": "Voice Control", "type": "webview"}]}, "commands": [{"command": "voicehype.startRecording", "title": "Start Voice Recording (Basic)", "icon": "$(record)", "category": "Voice"}, {"command": "voicehype.startRecordingWithOptimize", "title": "Start Voice Recording (AI-Optimized)", "icon": "$(sparkle)", "category": "Voice"}, {"command": "voicehype.stopRecording", "title": "Stop Voice Recording (Transcribe Only)", "icon": "$(debug-stop)", "category": "Voice"}, {"command": "voicehype.stopRecordingAndOptimize", "title": "Stop Voice Recording and Optimize", "icon": "$(debug-stop) $(sparkle)", "category": "Voice"}, {"command": "voicehype.togglePauseResume", "title": "Pause/Resume Voice Recording", "icon": "$(debug-pause)", "category": "Voice"}, {"command": "voicehype.playLastRecording", "title": "Play Last Recording", "icon": "$(play)", "category": "Voice"}, {"command": "voicehype.clearHistory", "title": "Clear History", "icon": "$(clear-all)", "category": "Voice"}, {"command": "voicehype.toggleRecording", "title": "Toggle Voice Recording", "category": "Voice"}, {"command": "voicehype.optimizeSelectedText", "title": "Optimize Selected Text", "icon": "$(sparkle)", "category": "Voice"}, {"command": "voicehype.toggleOptimizationMode", "title": "Toggle AI Optimization Mode", "category": "Voice"}, {"command": "voicehype.showQuickSettings", "title": "Show VoiceHype Quick Settings", "icon": "$(settings-gear)", "category": "Voice"}, {"command": "voicehype.selectOptimizationModel", "title": "Optimization Model", "icon": "$(sparkle)", "category": "Voice"}, {"command": "voicehype.pasteOriginalTranscript", "title": "Paste Original Transcript", "icon": "$(file-text)", "category": "Voice"}, {"command": "voicehype.toggleRealtimeTranscription", "title": "Toggle Real-time Transcription Mode", "icon": "$(pulse)", "category": "Voice"}, {"command": "voicehype.cancelRecording", "title": "Cancel Recording", "icon": "$(close)", "category": "VoiceHype"}], "menus": {"commandPalette": [{"command": "voicehype.startRecording", "when": "!voicehype.isRecording"}, {"command": "voicehype.startRecordingWithOptimize", "when": "!voicehype.isRecording"}, {"command": "voicehype.stopRecording", "when": "voicehype.isRecording"}, {"command": "voicehype.stopRecordingAndOptimize", "when": "voicehype.isRecording"}, {"command": "voicehype.playLastRecording", "when": "voicehype.hasRecording"}, {"command": "voicehype.cancelRecording", "when": "voicehype.isRecording"}], "view/title": [{"command": "voicehype.clearHistory", "when": "view == voicehypeHistory", "group": "navigation"}, {"command": "voicehype.cancelRecording", "when": "view == voicehype.controlPanel && voicehype.isRecording", "group": "navigation"}], "editor/title": [{"command": "voicehype.startRecording", "when": "!voicehype.isRecording", "group": "navigation"}, {"command": "voicehype.startRecordingWithOptimize", "when": "!voicehype.isRecording", "group": "navigation"}, {"command": "voicehype.stopRecording", "when": "voicehype.isRecording && !voicehype.isPaused", "group": "navigation"}, {"command": "voicehype.togglePauseResume", "when": "voicehype.isRecording && !voicehype.isPaused", "group": "navigation"}, {"command": "voicehype.togglePauseResume", "when": "voicehype.isRecording && voicehype.isPaused", "icon": "$(debug-continue)", "group": "navigation"}, {"command": "voicehype.cancelRecording", "when": "voicehype.isRecording", "group": "navigation"}], "editor/context": [{"command": "voicehype.optimizeSelectedText", "when": "editorHasSelection", "group": "VoiceHype"}]}, "keybindings": [{"command": "voicehype.startRecording", "key": "ctrl+shift+8", "mac": "cmd+shift+8", "when": "!voicehype.isRecording"}, {"command": "voicehype.startRecordingWithOptimize", "key": "ctrl+shift+9", "mac": "cmd+shift+9", "when": "!voicehype.isRecording"}, {"command": "voicehype.stopRecording", "key": "ctrl+shift+8", "mac": "cmd+shift+8", "when": "voicehype.isRecording"}, {"command": "voicehype.stopRecordingAndOptimize", "key": "ctrl+shift+9", "mac": "cmd+shift+9", "when": "voicehype.isRecording"}, {"command": "voicehype.togglePauseResume", "key": "ctrl+shift+0", "mac": "cmd+shift+0", "when": "voicehype.isRecording"}, {"command": "editor.action.clipboardPasteAction", "key": "ctrl+shift+v", "mac": "cmd+shift+v"}]}, "scripts": {"vscode:prepublish": "npm run package", "compile": "webpack", "watch": "webpack --watch", "package": "webpack --mode production --devtool hidden-source-map", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "test-mic": "ts-node src/test/test-microphone.ts", "test-sox": "ts-node ./src/test/soxPathTest.ts"}, "dependencies": {"@supabase/supabase-js": "^2.49.1", "assemblyai": "^3.1.3", "axios": "^1.6.5", "form-data": "^4.0.0", "load-esm": "^1.0.2", "music-metadata": "^11.1.1", "node-gyp": "^11.1.0", "node-microphone": "^0.1.6", "openai": "^4.85.1", "postgres": "^3.4.5", "sox-static": "^1.0.0"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@types/better-sqlite3": "^7.6.12", "@types/mocha": "^10.0.10", "@types/node": "^20.17.19", "@types/vscode": "^1.93.0", "@typescript-eslint/eslint-plugin": "^8.17.0", "@typescript-eslint/parser": "^8.17.0", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.4.1", "babel-loader": "^10.0.0", "bufferutil": "^4.0.9", "copy-webpack-plugin": "^13.0.0", "electron-rebuild": "^3.2.9", "eslint": "^9.16.0", "file-loader": "^6.2.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "typescript": "^5.7.3", "utf-8-validate": "^6.0.5", "webpack": "^5.98.0", "webpack-cli": "^6.0.1"}, "keywords": ["voice", "transcription", "productivity", "ai", "prompt"], "author": "VoiceHype (Kifayat <PERSON> & Bilal Tariq)", "license": "SEE LICENSE IN LICENSE.md"}