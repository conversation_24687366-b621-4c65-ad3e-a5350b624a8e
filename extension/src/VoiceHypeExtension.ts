import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';

// Import services
import { ConfigurationService } from './services/ConfigurationService';
import { StatusBarService } from './services/StatusBarService';
import { FileTrackingService } from './services/FileTrackingService';
import { HistoryService } from './services/HistoryService';
import { TranscriptionService } from './services/TranscriptionService';
import { RecordingService } from './services/RecordingService';
import { CommandService } from './services/CommandService';
import { VoiceHypePanel } from './services/VoiceHypePanelService';

export class VoiceHypeExtension {
    private configService: ConfigurationService;
    private statusBarService: StatusBarService;
    private fileTrackingService: FileTrackingService;
    private historyService: HistoryService;
    private transcriptionService: TranscriptionService;
    private recordingService: RecordingService;
    private commandService: CommandService;
    private voiceHypePanel: VoiceHypePanel;

    constructor(private context: vscode.ExtensionContext) {
        console.log('VoiceHype: Initializing services...');

        // Initialize services with dependency injection
        this.configService = new ConfigurationService();
        this.statusBarService = new StatusBarService(this.configService);
        this.fileTrackingService = new FileTrackingService();
        this.transcriptionService = new TranscriptionService(this.configService);
        this.historyService = new HistoryService(context, this.transcriptionService);
        this.recordingService = new RecordingService(
            context,
            this.configService,
            this.statusBarService,
            this.fileTrackingService,
            this.transcriptionService,
            this.historyService
        );

        // Create and register the webview panel
        this.voiceHypePanel = new VoiceHypePanel(
            context.extensionUri,
            this.configService,
            this.recordingService,
            this.transcriptionService,
            this.historyService,
            this.recordingService,
            this.configService,
            this.historyService
        );

        // Set the VoiceHypePanel reference in RecordingService for webview updates
        this.recordingService.setVoiceHypePanel(this.voiceHypePanel);

        this.commandService = new CommandService(
            context,
            this.recordingService,
            this.configService,
            this.transcriptionService,
            this.historyService,
            this.statusBarService
        );

        console.log('VoiceHype: Services initialized');
    }

    public activate(): void {
        console.log('VoiceHype: Starting activation...');

        // Register the webview view provider
        vscode.window.registerWebviewViewProvider(
            VoiceHypePanel.viewType,
            this.voiceHypePanel,
            { webviewOptions: { retainContextWhenHidden: true } }
        );

        // Register history commands - REMOVED - No longer needed
        // const historyCommands = this.historyService.registerCommands();
        // this.context.subscriptions.push(...historyCommands);

        // Register all commands
        this.commandService.registerCommands();

        // Check for API key on startup
        const apiKey = this.configService.getApiKey();
        if (!apiKey) {
            const message = 'VoiceHype API key not set. Please set your API key in settings. You can create one at voicehype.ai';
            vscode.window.showWarningMessage(message, 'Open Settings', 'Visit voicehype.ai').then(selection => {
                if (selection === 'Open Settings') {
                    vscode.commands.executeCommand('workbench.action.openSettings', 'voicehype.apiKey');
                } else if (selection === 'Visit voicehype.ai') {
                    vscode.env.openExternal(vscode.Uri.parse('https://voicehype.ai'));
                }
            });
        }

        // Register configuration change listener
        this.context.subscriptions.push(
            vscode.workspace.onDidChangeConfiguration(e => {
                if (e.affectsConfiguration('voicehype.apiKey')) {
                    const newApiKey = this.configService.getApiKey();
                    if (newApiKey) {
                        vscode.window.showInformationMessage('VoiceHype API key updated successfully!');
                    }
                }

                // Handle real-time transcription setting changes from VS Code settings
                if (e.affectsConfiguration('voicehype.transcription.realtime')) {
                    const isRealtimeEnabled = this.configService.getTranscriptionRealtime();
                    console.log(`VoiceHype: Real-time transcription setting changed to ${isRealtimeEnabled}`);

                    // Handle sample rate adjustment for real-time mode
                    this.configService.handleRealtimeToggle(isRealtimeEnabled).then(() => {
                        if (isRealtimeEnabled) {
                            const currentService = this.configService.getTranscriptionService();
                            if (currentService !== 'openai') { // Only show for AssemblyAI
                                vscode.window.showInformationMessage(
                                    'Real-time transcription enabled. Sample rate set to 16000Hz for optimal performance.'
                                );
                            }
                        }
                    });
                }

                // Stop recording and process with new settings if configuration changes during recording
                if (this.recordingService.isRecording() &&
                    (e.affectsConfiguration('voicehype.transcription.service') ||
                     e.affectsConfiguration('voicehype.transcription.model') ||
                     e.affectsConfiguration('voicehype.transcription.language'))) {

                    // Get the current configuration
                    const service = this.configService.getTranscriptionService();
                    const model = this.configService.getTranscriptionModel();
                    const language = this.configService.getTranscriptionLanguage();

                    // Stop the recording and process with the latest configuration
                    this.recordingService.stopRecording(this.configService.getShouldOptimize())
                        .then(() => {
                            // Show notification about the change
                            vscode.window.showInformationMessage(
                                `Recording stopped and processed with updated settings: ${service} (${model}) - ${language}`
                            );

                            // Update UI to reflect recording stopped
                            this.voiceHypePanel.updateRecordingState();
                        })
                        .catch(error => {
                            vscode.window.showErrorMessage(`Error processing recording: ${error.message}`);

                            // Update UI even on error
                            this.voiceHypePanel.updateRecordingState();
                        });
                }
            })
        );

        // Add all services to disposables
        this.context.subscriptions.push(this.configService);
        this.context.subscriptions.push(this.statusBarService);
        this.context.subscriptions.push(this.fileTrackingService);
        this.context.subscriptions.push(this.recordingService);
        this.context.subscriptions.push(this.historyService);
        this.context.subscriptions.push(this.commandService);

        // Add configuration contribution to package.json
        this.updatePackageJson();

        console.log('VoiceHype: Extension is now active!');
        if (apiKey) {
            vscode.window.showInformationMessage('VoiceHype is now active! Use Ctrl+Shift+8 for basic transcription or Ctrl+Shift+9 for AI-optimized version.');
        }
    }

    private updatePackageJson(): void {
        const packageJsonPath = path.join(this.context.extensionPath, 'package.json');
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

        if (!packageJson.contributes) {
            packageJson.contributes = {};
        }
        if (!packageJson.contributes.configuration) {
            packageJson.contributes.configuration = {
                title: 'VoiceHype',
                properties: {}
            };
        }

        // Add API configuration
        packageJson.contributes.configuration.properties['voicehype.apiKey'] = {
            type: 'string',
            default: '',
            description: 'API key for VoiceHype services. You can create one at voicehype.ai'
        };

        // Save updated package.json
        fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    }

    public deactivate(): void {
        // Nothing to do here, as all disposables are registered with the context
    }

    /**
     * Get a service instance by name
     * This allows other parts of the extension to get a reference to specific services
     * @param serviceName The name of the service to get
     * @returns The service instance or undefined if not found
     */
    public getService(serviceName: string): any {
        switch (serviceName) {
            case 'voiceHypePanel':
                return this.voiceHypePanel;
            case 'historyService':
                return this.historyService;
            case 'recordingService':
                return this.recordingService;
            case 'transcriptionService':
                return this.transcriptionService;
            case 'configService':
                return this.configService;
            default:
                return undefined;
        }
    }

    // Add a dispose method to clean up resources
    public dispose(): void {
        // Clean up all services
        if (this.statusBarService) {
            this.statusBarService.dispose();
        }
        if (this.fileTrackingService) {
            this.fileTrackingService.dispose();
        }
        if (this.recordingService) {
            this.recordingService.dispose();
        }
        if (this.commandService) {
            this.commandService.dispose();
        }
        if (this.historyService) {
            this.historyService.dispose();
        }
        if (this.configService) {
            this.configService.dispose();
        }
        if (this.voiceHypePanel) {
            this.voiceHypePanel.dispose();
        }
    }
}