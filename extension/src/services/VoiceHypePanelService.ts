import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { getNonce } from '../utils/getNonce';
import { ConfigurationService } from './ConfigurationService';
import { RecordingService } from './RecordingService';
import { TranscriptionService } from './TranscriptionService';
import { HistoryService } from './HistoryService';
import { IRecordingService } from '../models/interfaces';
import { IConfigurationService } from '../models/interfaces';
import { IHistoryService } from '../models/interfaces';

interface PromptMode {
    id: string;
    name: string;
    prompt: string;
    isCustom?: boolean;
}

export class VoiceHypePanel implements vscode.WebviewViewProvider {
    public static readonly viewType = 'voicehype.controlPanel';
    private _view?: vscode.WebviewView;

    // Track when options have just been updated to avoid feedback loops
    private _recentlyUpdatedOptions: Set<string> = new Set();
    private _isProcessingCommand = false;
    private _hasShownCustomPromptError = false;
    private _disposables: vscode.Disposable[] = [];
    private _previousSampleRate: number = 44100; // Store previous sample rate
    private _promptModes: PromptMode[] = []; // Store available prompt modes

    // Default prompt modes
    private _defaultPromptModes: PromptMode[] = [
        {
            id: 'default',
            name: 'Default',
            prompt: '',
            isCustom: false
        },
        {
            id: 'code',
            name: 'Code',
            prompt: 'Format as code with proper indentation and syntax.',
            isCustom: false
        },
        {
            id: 'markdown',
            name: 'Markdown',
            prompt: 'Format as markdown with headings, lists, and code blocks as appropriate.',
            isCustom: false
        }
    ];

    constructor(
        private readonly _extensionUri: vscode.Uri,
        private configurationService: ConfigurationService,
        private recordingService: RecordingService,
        private transcriptionService: TranscriptionService,
        private historyService: HistoryService,
        private _recordingService: IRecordingService,
        private _configService: IConfigurationService,
        private _historyService: IHistoryService
    ) {
        // Listen for configuration changes from other sources like quick settings menu
        this._disposables.push(
            this.configurationService.onDidChangeConfiguration(key => {
                console.log(`VoiceHypePanelService: Configuration changed - ${key}`);
                // Update the webview when settings change from outside
                this._forceSendConfiguration(true);
            })
        );

        // Listen for recording state changes
        this.recordingService.onStateChange((_isRecording: boolean, _isPaused: boolean) => {
            this._updateRecordingState();
        });

        // Register the recording state listener
        this._registerRecordingStateListener();
        // Load custom prompt modes from configuration
        this._loadPromptModesFromConfig();
    }

    /**
     * Load custom prompt modes from VS Code configuration
     * This ensures custom modes persist across VS Code sessions
     */
    private _loadPromptModesFromConfig(): void {
        try {
            // Get custom modes from configuration
            const promptModesConfig = this.configurationService.getPromptModes();
            const customModes = promptModesConfig.custom || [];
            const activeMode = promptModesConfig.active || 'clean-up';

            console.log(`VoiceHype: Loading ${customModes.length} custom prompt modes from configuration`);
            console.log(`VoiceHype: Active prompt mode is "${activeMode}"`);

            // Initialize with default modes
            this._promptModes = [...this._defaultPromptModes];

            // Add custom modes from configuration
            if (customModes && customModes.length > 0) {
                // Ensure all custom modes have the isCustom flag set
                const validCustomModes = customModes.map(mode => ({
                    ...mode,
                    isCustom: true
                }));

                // Log details of loaded custom modes for debugging
                validCustomModes.forEach(mode => {
                    console.log(`VoiceHype: Loaded custom mode "${mode.name}" with ID "${mode.id}"`);
                });

                // Add to prompt modes
                this._promptModes = [...this._promptModes, ...validCustomModes];
                console.log(`VoiceHype: Successfully loaded ${validCustomModes.length} custom prompt modes`);
            } else {
                console.log('VoiceHype: No custom prompt modes found in configuration');
            }
        } catch (error) {
            console.error('VoiceHype: Error loading custom prompt modes from configuration:', error);
        }
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        _context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        // Create a specific resource root for the webview build directory
        const webviewBuildPath = vscode.Uri.joinPath(this._extensionUri, 'webview-ui', 'build');

        // Get the workspace root directory
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri;

        webviewView.webview.options = {
            // Enable JavaScript in the webview
            enableScripts: true,
            // Restrict the webview to only load resources from the extension's directory and workspace root
            localResourceRoots: [
                this._extensionUri,
                webviewBuildPath,
                workspaceRoot // Allow access to workspace root for logo
            ].filter(Boolean) as vscode.Uri[] // Filter out undefined values
        };

        // Set the HTML content
        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // Send initial configuration immediately
        this._sendInitialConfiguration(webviewView.webview);

        // Send initial state immediately after view is resolved
        this._updateRecordingState();

        // Handle messages from the webview
        this._registerMessageListener();
    }

    private _sendInitialConfiguration(webview: vscode.Webview): void {
        // Get prompt modes configuration
        const promptModesConfig = this.configurationService.getPromptModes();

        // Send initial configuration to the webview
        const initialConfig = {
            service: this.configurationService.getTranscriptionService(),
            model: this.configurationService.getTranscriptionModel(),
            language: this.configurationService.getTranscriptionLanguage(),
            optimize: this.configurationService.getShouldOptimize(),
            optimizationModel: this.configurationService.getOptimizationModel(),
            customPrompt: this.configurationService.getCustomPrompt(),
            translate: this.configurationService.getTranslate(),
            realtime: this.configurationService.getTranscriptionRealtime(),
            apiKey: this.configurationService.getApiKey() || '',
            audioDevice: this.configurationService.getAudioDevice() || '',
            audioSettings: this.configurationService.getAudioSettings(),
            activePromptMode: promptModesConfig.active || 'clean-up',
            customPromptModes: this._promptModes.filter(m => m.isCustom)
        };

        // Log before sending to see exactly what's being sent
        console.log('[VoiceHypePanel] Sending initial configuration to webview:', JSON.stringify(initialConfig));

        // Verify the values are as expected
        console.log('[VoiceHypePanel] Config verification:');
        console.log(`Service from config: ${this.configurationService.getTranscriptionService()}`);
        console.log(`Model from config: ${this.configurationService.getTranscriptionModel()}`);
        console.log(`Language from config: ${this.configurationService.getTranscriptionLanguage()}`);
        console.log(`Optimize from config: ${this.configurationService.getShouldOptimize()}`);
        console.log(`Custom prompt from config (length): ${this.configurationService.getCustomPrompt().length}`);
        console.log(`Translate from config: ${this.configurationService.getTranslate()}`);
        console.log(`Realtime from config: ${this.configurationService.getTranscriptionRealtime()}`);

        webview.postMessage({
            command: 'initialConfiguration',
            options: initialConfig
        });

        // We've removed the automatic retry mechanism that was causing frequent updates
        // This was contributing to the janky UI behavior
        // The initial configuration should be sufficient
    }

    /**
     * Force-send current configuration to the WebView
     * @param force Set to true to force the WebView to apply the update regardless of recent changes
     */
    private _forceSendConfiguration(force: boolean = false): void {
        if (!this._view) {
            return;
        }

        // If force=true, clear the recently updated options to ensure all options are sent
        if (force) {
            this._recentlyUpdatedOptions.clear();
        }

        const config = {
            service: this.configurationService.getTranscriptionService(),
            model: this.configurationService.getTranscriptionModel(),
            language: this.configurationService.getTranscriptionLanguage(),
            optimize: this.configurationService.getShouldOptimize(),
            optimizationModel: this.configurationService.getOptimizationModel(),
            customPrompt: this.configurationService.getCustomPrompt(),
            translate: this.configurationService.getTranslate(),
            realtime: this.configurationService.getTranscriptionRealtime(),
            audioDevice: this.configurationService.getAudioDevice() || ''
        };

        console.log(`[VoiceHypePanel] Force-sending configuration to webview (force=${force}):`, config);

        this._view.webview.postMessage({
            command: 'updateOptions',
            options: config,
            force: force
        });
    }

    private _markOptionAsRecentlyUpdated(option: string): void {
        this._recentlyUpdatedOptions.add(option);

        // Automatically clear the flag after a delay
        setTimeout(() => {
            this._recentlyUpdatedOptions.delete(option);
        }, 2000); // 2 seconds should be enough to avoid feedback loops
    }

    private _updateRecordingState(): void {
        if (!this._view) {
            return;
        }

        // Get the current state directly from the recording service
        const isRecording = this.recordingService.isRecording();
        const isPaused = this.recordingService.isPaused();
        const elapsedTime = this.recordingService.getElapsedTime();

        console.log('VoiceHype [DEBUG]: Updating WebView recording state:', { isRecording, isPaused, elapsedTime });
        console.log('VoiceHype [DEBUG]: WebView update called from:', new Error().stack);

        // Send current state to webview
        this._view.webview.postMessage({
            command: 'recordingState',
            isRecording,
            isPaused,
            elapsedTime
        });
    }

    /**
     * Update the recording state in the UI when stopped externally
     * This should be called by other services when they stop recording
     */
    public updateRecordingState(): void {
        this._updateRecordingState();
    }

    /**
     * Update the transcription history in the WebView
     * This should be called after new entries are added to history
     */
    public async updateTranscriptionHistory(): Promise<void> {
        console.log('VoiceHype: updateTranscriptionHistory() called');

        if (!this._view) {
            console.log('VoiceHype: Cannot update WebView transcription history - no view available');
            return;
        }

        if (!this._view.webview) {
            console.log('VoiceHype: Cannot update WebView transcription history - webview not available');
            return;
        }

        console.log('VoiceHype: WebView and webview are available, proceeding with update');
        console.log('VoiceHype: WebView visible state:', this._view.visible);
        console.log('VoiceHype: WebView active state:', this._view.active);

        // Get the latest history
        const history = await this.historyService.getHistory();
        console.log(`VoiceHype: Retrieved ${history.length} transcriptions from history service`);

        if (history.length > 0) {
            // Log the most recent entry for debugging
            const latest = history[history.length - 1];
            console.log('VoiceHype: Most recent transcription:', {
                timestamp: new Date(latest.timestamp).toISOString(),
                textLength: latest.transcript.length,
                hasOptimized: !!latest.optimizedTranscript,
                service: latest.service || 'unknown',
                model: latest.model || 'unknown'
            });
        }

        // Format history for webview
        const formattedHistory = history.map(entry => ({
            id: entry.timestamp.toString(),
            timestamp: new Date(entry.timestamp).toISOString(),
            originalText: entry.transcript,
            optimizedText: entry.optimizedTranscript,
            service: entry.service || 'unknown',
            model: entry.model || 'unknown',
            language: entry.language || 'en',
            duration: entry.duration
        }));

        // Send to WebView
        try {
            console.log(`VoiceHype: Sending ${formattedHistory.length} transcriptions to WebView`);
            console.log('VoiceHype: Message payload:', {
                command: 'updateTranscriptions',
                transcriptionsCount: formattedHistory.length,
                firstTranscription: formattedHistory.length > 0 ? {
                    id: formattedHistory[0].id,
                    timestamp: formattedHistory[0].timestamp,
                    textLength: formattedHistory[0].originalText.length
                } : null
            });

            this._view.webview.postMessage({
                command: 'updateTranscriptions',
                transcriptions: formattedHistory
            });
            console.log('VoiceHype: Successfully sent transcription update to WebView');

            // If webview is not visible/active, try again after a short delay
            if (!this._view.visible || !this._view.active) {
                console.log('VoiceHype: WebView not visible/active, scheduling retry in 1 second');
                setTimeout(() => {
                    if (this._view && this._view.webview) {
                        console.log('VoiceHype: Retrying transcription update to WebView');
                        this._view.webview.postMessage({
                            command: 'updateTranscriptions',
                            transcriptions: formattedHistory
                        });
                    }
                }, 1000);
            }
        } catch (error) {
            console.error('VoiceHype: Error sending transcription history to WebView:', error);
        }
    }

    private _sendCurrentOptionsToWebview(): void {
        if (!this._view) {
            return;
        }

        // Don't send options if we're currently processing a command
        if (this._isProcessingCommand) {
            console.log('Skipping options update while processing command');
            return;
        }

        // Create options object with only the properties that haven't been recently updated
        const options: Record<string, any> = {};

        // Only add options that haven't been recently updated by the UI
        if (!this._recentlyUpdatedOptions.has('service')) {
            options.service = this.configurationService.getTranscriptionService();
        }
        if (!this._recentlyUpdatedOptions.has('model')) {
            options.model = this.configurationService.getTranscriptionModel();
        }
        if (!this._recentlyUpdatedOptions.has('language')) {
            options.language = this.configurationService.getTranscriptionLanguage();
        }
        if (!this._recentlyUpdatedOptions.has('optimize')) {
            options.optimize = this.configurationService.getShouldOptimize();
        }
        if (!this._recentlyUpdatedOptions.has('optimizationModel')) {
            options.optimizationModel = this.configurationService.getOptimizationModel();
        }
        if (!this._recentlyUpdatedOptions.has('customPrompt')) {
            options.customPrompt = this.configurationService.getCustomPrompt();
        }
        if (!this._recentlyUpdatedOptions.has('translate')) {
            options.translate = this.configurationService.getTranslate();
        }
        if (!this._recentlyUpdatedOptions.has('realtime')) {
            options.realtime = this.configurationService.getTranscriptionRealtime();
        }

        // Only send the message if there are options to update
        if (Object.keys(options).length > 0) {
            console.log('Sending options to webview:', options);
            this._view.webview.postMessage({
                command: 'updateOptions',
                options
            });
        }
    }

    private _getHtmlForWebview(webview: vscode.Webview): string {
        console.log('Building webview HTML content...');

        // Get the local paths for webview resources
        const webviewBuildPath = path.join('webview-ui', 'build');
        const webviewStaticPath = path.join(webviewBuildPath, 'static');

        // Use a nonce to only allow specific scripts to be run
        const nonce = getNonce();

        // Find the main JS and CSS files from the React build
        let scriptPath = '';
        let stylePath = '';
        let runtimeScriptPath = '';
        let vendorScriptPath = '';

        try {
            // Debug log for WebView base path
            console.log(`WebView build path: ${path.join(this._extensionUri.fsPath, webviewBuildPath)}`);

            // React build places files in 'static/js' and 'static/css' with hash filenames
            const jsPath = path.join(this._extensionUri.fsPath, webviewStaticPath, 'js');
            const cssPath = path.join(this._extensionUri.fsPath, webviewStaticPath, 'css');

            console.log(`Looking for JS files in: ${jsPath}`);
            console.log(`Looking for CSS files in: ${cssPath}`);

            const jsFiles = fs.readdirSync(jsPath);
            const mainJsFiles = jsFiles.filter(file => file.startsWith('main.') && file.endsWith('.chunk.js'));
            const runtimeJsFiles = jsFiles.filter(file => file.startsWith('runtime') && file.endsWith('.js'));
            const vendorJsFiles = jsFiles.filter(file => file.indexOf('2.') > -1 && file.endsWith('.chunk.js'));

            const cssFiles = fs.readdirSync(cssPath).filter(file => file.startsWith('main.') && file.endsWith('.chunk.css'));

            if (mainJsFiles.length > 0) {
                scriptPath = path.join(webviewStaticPath, 'js', mainJsFiles[0]);
                console.log(`Found JS file: ${scriptPath}`);
            } else {
                console.error('No main.*.chunk.js file found in build/static/js directory');
            }

            if (runtimeJsFiles.length > 0) {
                runtimeScriptPath = path.join(webviewStaticPath, 'js', runtimeJsFiles[0]);
                console.log(`Found runtime JS file: ${runtimeScriptPath}`);
            }

            if (vendorJsFiles.length > 0) {
                vendorScriptPath = path.join(webviewStaticPath, 'js', vendorJsFiles[0]);
                console.log(`Found vendor JS file: ${vendorScriptPath}`);
            }

            if (cssFiles.length > 0) {
                stylePath = path.join(webviewStaticPath, 'css', cssFiles[0]);
                console.log(`Found CSS file: ${stylePath}`);
            } else {
                console.error('No main.*.css file found in build/static/css directory');
            }
        } catch (error) {
            console.error('Error finding webview resources:', error);
            // Fallbacks if files cannot be found dynamically
            scriptPath = path.join(webviewStaticPath, 'js', 'main.chunk.js');
            stylePath = path.join(webviewStaticPath, 'css', 'main.chunk.css');
        }

        // Convert the local paths to webview URIs
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, scriptPath));
        const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, stylePath));
        const runtimeScriptUri = runtimeScriptPath ?
            webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, runtimeScriptPath)) : '';
        const vendorScriptUri = vendorScriptPath ?
            webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, vendorScriptPath)) : '';

        // Create URIs for both light and dark mode logos
        const logoLightUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'voicehype_logo.png'));
        const logoDarkUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'voicehype_logo_dark.png'));

        // Debug log for resources
        console.log(`Script URI: ${scriptUri}`);
        console.log(`Style URI: ${styleUri}`);
        console.log(`Logo Light URI: ${logoLightUri}`);
        console.log(`Logo Dark URI: ${logoDarkUri}`);

        // Improved CSP to allow React to work properly and load images from workspace
        const csp = `default-src 'none';
                    style-src ${webview.cspSource} 'unsafe-inline';
                    script-src ${webview.cspSource} 'unsafe-inline' 'unsafe-eval';
                    img-src ${webview.cspSource} https: data:;
                    font-src ${webview.cspSource};
                    connect-src ${webview.cspSource} https:;`;

        return `<!DOCTYPE html>
            <html lang="en" class="${this._getVSCodeThemeClass()}">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <meta http-equiv="Content-Security-Policy" content="${csp}">
                <link href="${styleUri}" rel="stylesheet">
                <title>VoiceHype Control Panel</title>
                <script>
                    window.voicehypeLogoLight = "${logoLightUri.toString()}";
                    window.voicehypeLogoDark = "${logoDarkUri.toString()}";
                </script>
            </head>
            <body class="${this._getVSCodeThemeClass()}">
                <div id="root"></div>
                ${runtimeScriptUri ? `<script src="${runtimeScriptUri}"></script>` : ''}
                ${vendorScriptUri ? `<script src="${vendorScriptUri}"></script>` : ''}
                <script src="${scriptUri}"></script>
            </body>
            </html>`;
    }

    private _getVSCodeThemeClass(): string {
        // Get VS Code theme based on current color theme kind
        const themeKind = vscode.window.activeColorTheme.kind;
        return themeKind === vscode.ColorThemeKind.Dark || themeKind === vscode.ColorThemeKind.HighContrast
            ? 'vscode-dark'
            : 'vscode-light';
    }

    public dispose(): void {
        // Clean up all disposables
        this._disposables.forEach(d => d.dispose());
    }

    private _registerRecordingStateListener(): void {
        // Listen for recording state changes
        this.recordingService.onStateChange((isRecording: boolean, isPaused: boolean, elapsedTime: number) => {
            console.log(`VoiceHype [DEBUG]: Recording state change listener triggered: isRecording=${isRecording}, isPaused=${isPaused}`);
            if (this._view) {
                console.log('VoiceHype [DEBUG]: Sending recording state to webview from listener');
                this._view.webview.postMessage({
                    command: 'recordingState',
                    isRecording,
                    isPaused,
                    elapsedTime
                });
            }
        });
    }

    private async _handleRealtimeToggle(realtime: boolean): Promise<void> {
        // Get current audio settings
        const currentSettings = this.configurationService.getAudioSettings();
        const currentSampleRate = currentSettings.sampleRate;
        const currentService = this.configurationService.getTranscriptionService();

        console.log(`VoiceHype: Handling realtime toggle. Current sample rate: ${currentSampleRate}Hz, Realtime: ${realtime}, Service: ${currentService}`);

        // Skip sample rate adjustment in these cases:
        // 1. When we're processing a command (starting recording)
        // 2. When the service is Whisper (openai) as it doesn't support real-time
        if (this._isProcessingCommand) {
            console.log('VoiceHype: Skipping sample rate adjustment during recording start');
            return;
        }

        if (currentService === 'openai') {
            console.log('VoiceHype: Whisper service detected, skipping sample rate adjustment');
            // Force real-time off for Whisper service
            if (realtime) {
                console.log('VoiceHype: Forcing real-time off for Whisper service');
                await this.configurationService.updateSetting('voicehype.transcription.realtime', false);
                this._markOptionAsRecentlyUpdated('realtime');
            }
            return;
        }

        if (realtime) {
            // Store current sample rate before switching to real-time
            this._previousSampleRate = currentSampleRate;

            // Only change if not already at 16000 Hz
            if (currentSampleRate !== 16000) {
                console.log(`VoiceHype: Changing sample rate from ${currentSampleRate}Hz to 16000Hz for real-time mode`);

                // Set sample rate to 16000 Hz for real-time
                await this.configurationService.updateSetting('voicehype.audio.sampleRate', 16000);

                // No need to send to WebView - it's already updated optimistically
            } else {
                console.log('VoiceHype: Sample rate already at 16000Hz, no change needed');
            }
        } else {
            // Only restore if we have a previous sample rate and it's different
            if (this._previousSampleRate && this._previousSampleRate !== currentSampleRate) {
                console.log(`VoiceHype: Restoring sample rate from ${currentSampleRate}Hz to ${this._previousSampleRate}Hz`);

                // Restore previous sample rate
                await this.configurationService.updateSetting('voicehype.audio.sampleRate', this._previousSampleRate);

                // No need to send to WebView - it's already updated optimistically
            } else {
                console.log('VoiceHype: No sample rate restoration needed');
            }
        }
    }

    private _registerMessageListener(): void {
        if (!this._view) {
            return;
        }

        this._view.webview.onDidReceiveMessage(async (message) => {
            try {
                switch (message.command) {
                    case 'updateAudioDevice':
                        if (message.deviceId !== undefined) {
                            console.log('VoiceHype: Updating audio device to:', message.deviceId);
                            await this.configurationService.updateSetting('voicehype.audio.device', message.deviceId);
                            this._markOptionAsRecentlyUpdated('audioDevice');
                        }
                        break;

                    case 'getPromptModes':
                        // Get active mode ID from configuration
                        const promptModesConfig = this.configurationService.getPromptModes();
                        const activeMode = promptModesConfig.active || 'clean-up';

                        console.log(`VoiceHype: Sending prompt modes to webview with activeMode: ${activeMode}`);
                        console.log(`VoiceHype: Custom modes count: ${this._promptModes.filter(m => m.isCustom).length}`);

                        this._view?.webview.postMessage({
                            command: 'promptModes',
                            modes: this._promptModes,
                            activeMode: activeMode
                        });
                        break;

                    case 'savePromptMode':
                        if (message.mode) {
                            // Make sure mode has isCustom flag
                            const customMode = {
                                ...message.mode,
                                isCustom: true
                            };

                            console.log(`VoiceHype: Saving custom prompt mode "${customMode.name}"`);

                            // Add to in-memory store
                            this._promptModes = [...this._promptModes, customMode];

                            // Get current custom modes only
                            const customModes = this._promptModes.filter(m => m.isCustom);
                            console.log(`VoiceHype: Total custom modes to save: ${customModes.length}`);

                            try {
                                // Persist to settings (global scope)
                                await this.configurationService.updateSetting(
                                    'voicehype.promptModes.custom',
                                    customModes
                                );

                                console.log(`VoiceHype: Successfully saved custom prompt mode "${customMode.name}" to global configuration`);
                            } catch (error) {
                                console.error(`VoiceHype: Error saving custom prompt mode:`, error);
                            }

                            // Send updated modes back to webview
                            this._view?.webview.postMessage({
                                command: 'promptModes',
                                modes: this._promptModes,
                                activeMode: this.configurationService.getPromptModes().active
                            });
                        }
                        break;

                    case 'setActivePromptMode':
                        if (message.modeId) {
                            console.log(`VoiceHype: Setting active prompt mode to "${message.modeId}"`);

                            try {
                                await this.configurationService.updateSetting(
                                    'voicehype.promptModes.active',
                                    message.modeId
                                );
                                console.log(`VoiceHype: Successfully saved active prompt mode "${message.modeId}" to configuration`);

                                // Confirm the change to the webview
                                this._view?.webview.postMessage({
                                    command: 'activePromptModeUpdated',
                                    modeId: message.modeId
                                });
                            } catch (error) {
                                console.error(`VoiceHype: Error setting active prompt mode:`, error);
                            }
                        }
                        break;

                    case 'deletePromptMode':
                        if (message.modeId) {
                            console.log(`VoiceHype: Deleting prompt mode with ID: ${message.modeId}`);

                            // Filter out the mode to delete
                            const updatedModes = this._promptModes.filter(mode => mode.id !== message.modeId);

                            // Only allow deleting custom modes
                            if (updatedModes.length < this._promptModes.length) {
                                // Update in-memory store
                                this._promptModes = updatedModes;

                                // Persist to settings
                                await this.configurationService.updateSetting(
                                    'voicehype.promptModes.custom',
                                    this._promptModes.filter(m => m.isCustom)
                                );

                                // Send updated modes back to webview
                                this._view?.webview.postMessage({
                                    command: 'promptModes',
                                    modes: this._promptModes
                                });

                                console.log(`VoiceHype: Prompt mode deleted and settings updated`);
                            }
                        }
                        break;
                    case 'startRecording':
                        // Set flag to indicate we're processing a command
                        this._isProcessingCommand = true;

                        const options = message.options || {};
                        // Update settings with the options received
                        if (options.service) {
                            await this.configurationService.updateSetting('voicehype.transcription.service', options.service);
                            this._markOptionAsRecentlyUpdated('service');
                        }
                        if (options.model) {
                            await this.configurationService.updateSetting('voicehype.transcription.model', options.model);
                            this._markOptionAsRecentlyUpdated('model');
                        }
                        if (options.language) {
                            await this.configurationService.updateSetting('voicehype.transcription.language', options.language);
                            this._markOptionAsRecentlyUpdated('language');
                        }
                        if (options.optimize !== undefined) {
                            await this.configurationService.updateSetting('voicehype.transcription.shouldOptimize', options.optimize);
                            this._markOptionAsRecentlyUpdated('optimize');
                        }
                        if (options.customPrompt !== undefined) {
                            await this.configurationService.updateSetting('voicehype.transcription.customPrompt', options.customPrompt);
                            this._markOptionAsRecentlyUpdated('customPrompt');
                        }
                        if (options.translate !== undefined) {
                            await this.configurationService.updateSetting('voicehype.transcription.translate', options.translate);
                            this._markOptionAsRecentlyUpdated('translate');
                        }
                        if (options.realtime !== undefined) {
                            // Check if we're switching to Whisper (openai) service with real-time on
                            // If so, force real-time off as it's not supported for Whisper
                            const currentService = this.configurationService.getTranscriptionService();
                            if (options.realtime && currentService === 'openai') {
                                console.log('VoiceHype: Whisper service detected, forcing real-time off');
                                options.realtime = false;
                            }

                            await this.configurationService.updateSetting('voicehype.transcription.realtime', options.realtime);
                            this._markOptionAsRecentlyUpdated('realtime');

                            // Only handle sample rate changes if real-time is enabled
                            if (options.realtime) {
                                console.log('VoiceHype: Real-time enabled, adjusting sample rate');
                                await this._handleRealtimeToggle(options.realtime);
                            } else {
                                console.log('VoiceHype: Real-time disabled, keeping current sample rate');
                            }
                        }
                        // Start recording
                        console.log('VoiceHype: Starting recording from webview with optimize:', options.optimize || false);
                        await this.recordingService.startRecording(options.optimize || false);

                        // Ensure VS Code context and status bar are updated
                        vscode.commands.executeCommand('setContext', 'voicehype.isRecording', true);
                        vscode.commands.executeCommand('setContext', 'voicehype.isPaused', false);

                        // Force update the status bar
                        const statusBarService = this.recordingService.getStatusBarService();
                        if (statusBarService) {
                            statusBarService.updateStatusBarItems(true, options.optimize || false, false);
                        }

                        // Reset the processing flag
                        this._isProcessingCommand = false;
                        break;

                    case 'stopRecording':
                        console.log('VoiceHype: Stopping recording from webview');
                        await this.recordingService.stopRecording(this.configurationService.getShouldOptimize());

                        // Ensure VS Code context and status bar are updated
                        vscode.commands.executeCommand('setContext', 'voicehype.isRecording', false);
                        vscode.commands.executeCommand('setContext', 'voicehype.isPaused', false);

                        // Force update the status bar
                        const stopStatusBarService = this.recordingService.getStatusBarService();
                        if (stopStatusBarService) {
                            stopStatusBarService.updateStatusBarItems(false, false, false);
                        }
                        break;

                    case 'pauseRecording':
                        console.log('VoiceHype: Pausing recording from webview');
                        await this.recordingService.pauseRecording();

                        // Ensure VS Code context is updated
                        vscode.commands.executeCommand('setContext', 'voicehype.isPaused', true);
                        break;

                    case 'resumeRecording':
                        console.log('VoiceHype: Resuming recording from webview');
                        await this.recordingService.resumeRecording();

                        // Ensure VS Code context is updated
                        vscode.commands.executeCommand('setContext', 'voicehype.isPaused', false);
                        break;

                    case 'cancelRecording':
                        console.log('VoiceHype: Cancelling recording from webview');
                        await this.recordingService.cancelRecording();

                        // Ensure VS Code context and status bar are updated
                        vscode.commands.executeCommand('setContext', 'voicehype.isRecording', false);
                        vscode.commands.executeCommand('setContext', 'voicehype.isPaused', false);

                        // Force update the status bar
                        const cancelStatusBarService = this.recordingService.getStatusBarService();
                        if (cancelStatusBarService) {
                            cancelStatusBarService.updateStatusBarItems(false, false, false);
                        }
                        break;

                    case 'getOptions':
                        this._sendCurrentOptionsToWebview();
                        break;

                    case 'getTranscriptions':
                        // Send all historical transcriptions to the webview
                        const history = await this.historyService.getHistory();
                        this._view?.webview.postMessage({
                            command: 'updateTranscriptions',
                            transcriptions: history.map(entry => ({
                                id: entry.timestamp.toString(),
                                timestamp: new Date(entry.timestamp).toISOString(),
                                originalText: entry.transcript,
                                optimizedText: entry.optimizedTranscript,
                                service: entry.service || 'unknown',
                                model: entry.model || 'unknown',
                                language: entry.language || 'en',
                                duration: entry.duration
                            }))
                        });
                        break;

                    case 'updateOptions':
                        // Set flag to indicate we're processing a command
                        this._isProcessingCommand = true;

                        if (message.options) {
                            const { service, model, language, customPrompt, optimize, optimizationModel, translate, realtime, apiKey } = message.options;

                            // Track which options we're updating to avoid sending them back
                            const updatedOptions: string[] = [];

                            if (service !== undefined) {
                                await this.configurationService.updateSetting('voicehype.transcription.service', service);
                                this._markOptionAsRecentlyUpdated('service');
                                updatedOptions.push('service');

                                // If service is changed to AssemblyAI, reset translate to false
                                if (service === 'assemblyai') {
                                    await this.configurationService.updateSetting('voicehype.transcription.translate', false);
                                    this._markOptionAsRecentlyUpdated('translate');
                                    updatedOptions.push('translate');
                                }

                                // If service is changed to Whisper (openai), ensure real-time is off
                                // This prevents the sample rate from being forced to 16000Hz
                                if (service === 'openai') {
                                    const currentRealtime = this.configurationService.getTranscriptionRealtime();
                                    if (currentRealtime) {
                                        console.log('VoiceHype: Switching to Whisper service, disabling real-time transcription');
                                        await this.configurationService.updateSetting('voicehype.transcription.realtime', false);
                                        this._markOptionAsRecentlyUpdated('realtime');
                                        updatedOptions.push('realtime');

                                        // Skip sample rate adjustment since we're turning off real-time
                                        this._isProcessingCommand = true;
                                    }
                                }
                            }
                            if (model !== undefined) {
                                await this.configurationService.updateSetting('voicehype.transcription.model', model);
                                this._markOptionAsRecentlyUpdated('model');
                                updatedOptions.push('model');
                            }
                            if (language !== undefined) {
                                await this.configurationService.updateSetting('voicehype.transcription.language', language);
                                this._markOptionAsRecentlyUpdated('language');
                                updatedOptions.push('language');
                            }
                            if (optimize !== undefined) {
                                await this.configurationService.updateSetting('voicehype.transcription.shouldOptimize', optimize);
                                this._markOptionAsRecentlyUpdated('optimize');
                                updatedOptions.push('optimize');
                            }
                            if (optimizationModel !== undefined) {
                                await this.configurationService.updateSetting('voicehype.transcription.optimizationModel', optimizationModel);
                                this._markOptionAsRecentlyUpdated('optimizationModel');
                                updatedOptions.push('optimizationModel');
                                console.log(`[VoiceHypePanel] Updated optimization model to: ${optimizationModel}`);
                            }
                            if (customPrompt !== undefined) {
                                try {
                                    console.log(`[VoiceHypePanel] Updating custom prompt, length: ${customPrompt.length}`);
                                    await this.configurationService.updateSetting('voicehype.transcription.customPrompt', customPrompt);
                                    this._markOptionAsRecentlyUpdated('customPrompt');
                                    updatedOptions.push('customPrompt');
                                    console.log(`[VoiceHypePanel] Successfully saved custom prompt to VS Code settings`);
                                } catch (error) {
                                    console.error('[VoiceHypePanel] Error saving custom prompt:', error);
                                    // Still mark as updated to prevent feedback loops
                                    this._markOptionAsRecentlyUpdated('customPrompt');
                                    // Don't add to updatedOptions list as it failed to save

                                    // We'll still use the custom prompt value for the current session
                                    // It's stored in memory in the ConfigurationService

                                    // Notify the webview of the error (only show once per session)
                                    if (!this._hasShownCustomPromptError) {
                                        this._hasShownCustomPromptError = true;
                                        this._view?.webview.postMessage({
                                            command: 'error',
                                            message: 'Custom prompt is temporarily stored but may not persist between sessions. Please restart VS Code after the first use.'
                                        });

                                        // Also show a VS Code warning
                                        vscode.window.showWarningMessage(
                                            'VoiceHype: Custom prompt saved in memory but may not persist. Please restart VS Code after using once to complete registration.'
                                        );
                                    }
                                }
                            }
                            if (translate !== undefined) {
                                await this.configurationService.updateSetting('voicehype.transcription.translate', translate);
                                this._markOptionAsRecentlyUpdated('translate');
                                updatedOptions.push('translate');
                            }
                            if (realtime !== undefined) {
                                await this.configurationService.updateSetting('voicehype.transcription.realtime', realtime);
                                this._markOptionAsRecentlyUpdated('realtime');

                                // Handle sample rate changes for real-time
                                await this._handleRealtimeToggle(realtime);
                            }
                            if (apiKey !== undefined) {
                                await this.configurationService.updateSetting('voicehype.apiKey', apiKey);
                                this._markOptionAsRecentlyUpdated('apiKey');
                                updatedOptions.push('apiKey');
                            }

                            // Log updated options for debugging
                            if (updatedOptions.length > 0) {
                                console.log(`[VoiceHypePanel] Updated options: ${updatedOptions.join(', ')}`);
                            }
                        }

                        // Reset the processing flag
                        this._isProcessingCommand = false;
                        break;

                    case 'copyToClipboard':
                        if (message.text) {
                            await vscode.env.clipboard.writeText(message.text);
                            vscode.window.showInformationMessage('Text copied to clipboard');
                        }
                        break;

                    case 'optimizeTranscription':
                        if (message.text) {
                            const optimized = await this.transcriptionService.optimizeText(message.text, message.customPrompt);
                            // Update the history entry if an ID was provided
                            if (message.id) {
                                // Find the entry by timestamp
                                const timestamp = parseInt(message.id, 10);
                                if (!isNaN(timestamp)) {
                                    // Update the entry in history
                                    const history = this.historyService.getHistory();
                                    const entryIndex = history.findIndex(entry => entry.timestamp === timestamp);
                                    if (entryIndex >= 0) {
                                        this.historyService.updateEntryWithOptimization(entryIndex, optimized);
                                    }
                                }
                            }
                            this._view?.webview.postMessage({
                                command: 'optimizationComplete',
                                id: message.id,
                                optimizedText: optimized
                            });
                        }
                        break;

                    case 'deleteTranscription':
                        if (message.id) {
                            const timestamp = parseInt(message.id, 10);
                            if (!isNaN(timestamp)) {
                                // Find the entry in history
                                const history = this.historyService.getHistory();
                                const entryIndex = history.findIndex(entry => entry.timestamp === timestamp);
                                if (entryIndex >= 0) {
                                    // Remove the entry from history
                                    history.splice(entryIndex, 1);
                                    await this.historyService.clearHistory();
                                    for (const entry of history) {
                                        this.historyService.addEntry(entry);
                                    }
                                    // Notify webview that the transcription was deleted
                                    this._view?.webview.postMessage({
                                        command: 'transcriptionDeleted',
                                        id: message.id
                                    });
                                }
                            }
                        }
                        break;

                    case 'clearTranscriptionHistory':
                        try {
                            console.log('VoiceHypePanel: Received clearTranscriptionHistory command');

                            // Clear history from both service instances to ensure consistency
                            this._historyService.clearHistory();
                            this.historyService.clearHistory();
                            console.log('VoiceHypePanel: History cleared in both service instances');

                            // Send empty transcriptions list to WebView
                            if (this._view) {
                                console.log('VoiceHypePanel: Sending messages to webview to clear history');

                                // First send updateTranscriptions with empty array
                                this._view.webview.postMessage({
                                    command: 'updateTranscriptions',
                                    transcriptions: []
                                });
                                console.log('VoiceHypePanel: Sent updateTranscriptions with empty array');

                                // Then send allTranscriptionsCleared message to ensure the webview clears its state
                                this._view.webview.postMessage({
                                    command: 'allTranscriptionsCleared'
                                });
                                console.log('VoiceHypePanel: Sent allTranscriptionsCleared message');

                                // Show confirmation message
                                vscode.window.showInformationMessage('Transcription history cleared');
                            } else {
                                console.error('VoiceHypePanel: View is not available, cannot send messages to webview');
                            }
                        } catch (error) {
                            console.error('VoiceHype: Error clearing transcription history:', error);
                            vscode.window.showErrorMessage('Failed to clear transcription history');
                        }
                        break;

                    case 'getAudioSettings':
                        this._sendAudioSettings();
                        break;

                    case 'getAudioDevices':
                        // Get available audio devices from the microphone class
                        const devices = await this.recordingService.getAvailableDevices();
                        this._view?.webview.postMessage({
                            command: 'updateAudioDevices',
                            devices: devices.map(device => ({
                                id: device.id,
                                name: device.name
                            }))
                        });
                        break;

                    case 'updateSetting':
                        if (message.key === 'voicehype.audio.sampleRate') {
                            console.log(`VoiceHype: Received sample rate update from WebView: ${message.value}`);
                            this.configurationService.updateSetting(message.key, message.value);
                            // No need to send settings back to WebView - it's already updated optimistically
                        }
                        break;

                    case 'showConfirmation':
                        if (message.message && message.onConfirm) {
                            console.log(`VoiceHypePanel: Received showConfirmation with onConfirm=${message.onConfirm}`);

                            const result = await vscode.window.showWarningMessage(
                                message.message,
                                { modal: true },
                                'Yes',
                                'No'
                            );

                            console.log(`VoiceHypePanel: User selected: ${result}`);

                            if (result === 'Yes') {
                                console.log(`VoiceHypePanel: Forwarding command: ${message.onConfirm}`);
                                // Handle the confirmation by sending the original command
                                await this._handleMessage({ command: message.onConfirm });
                            } else {
                                console.log('VoiceHypePanel: User cancelled the operation');
                            }
                        } else {
                            console.error('VoiceHypePanel: Received showConfirmation without message or onConfirm');
                        }
                        break;
                }
            } catch (error: any) {
                console.error('VoiceHype: Error handling message:', error);
                this._view?.webview.postMessage({
                    command: 'error',
                    message: error.message
                });
            }
        });
    }

    private _sendAudioSettings(): void {
        // Get the latest audio settings directly from the configuration service
        const settings = this.configurationService.getAudioSettings();
        console.log('VoiceHype: Sending audio settings to WebView:', settings);

        // Make sure we have a view before sending
        if (this._view) {
            // Send the current settings to the WebView
            this._view.webview.postMessage({
                command: 'updateAudioSettings',
                settings
            });
        } else {
            console.log('VoiceHype: Cannot send audio settings - no WebView available');
        }
    }

    private async _handleMessage(message: any): Promise<void> {
        console.log(`VoiceHypePanel: _handleMessage called with command: ${message.command}`);

        if (!this._view) {
            console.error('VoiceHypePanel: _handleMessage failed - view is not available');
            return;
        }

        // For clearTranscriptionHistory, we need to handle it directly instead of just forwarding
        if (message.command === 'clearTranscriptionHistory') {
            console.log('VoiceHypePanel: Handling clearTranscriptionHistory command directly');
            // Instead of using _onDidReceiveMessage, we'll handle the clearTranscriptionHistory case directly
            try {
                console.log('VoiceHypePanel: Directly clearing history');

                // Clear history from both service instances to ensure consistency
                this._historyService.clearHistory();
                this.historyService.clearHistory();
                console.log('VoiceHypePanel: History cleared in both service instances');

                // Send empty transcriptions list to WebView
                if (this._view) {
                    console.log('VoiceHypePanel: Sending messages to webview to clear history');

                    // First send updateTranscriptions with empty array
                    this._view.webview.postMessage({
                        command: 'updateTranscriptions',
                        transcriptions: []
                    });
                    console.log('VoiceHypePanel: Sent updateTranscriptions with empty array');

                    // Then send allTranscriptionsCleared message to ensure the webview clears its state
                    this._view.webview.postMessage({
                        command: 'allTranscriptionsCleared'
                    });
                    console.log('VoiceHypePanel: Sent allTranscriptionsCleared message');

                    // Show confirmation message
                    vscode.window.showInformationMessage('Transcription history cleared');
                }
            } catch (error) {
                console.error('VoiceHype: Error clearing transcription history:', error);
                vscode.window.showErrorMessage('Failed to clear transcription history');
            }
        } else {
            // For other messages, just forward to the webview
            console.log(`VoiceHypePanel: Forwarding message to webview: ${message.command}`);
            await this._view.webview.postMessage(message);
        }
    }
}