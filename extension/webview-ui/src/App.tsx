import * as React from 'react';
import { useState, useEffect, useCallback, useRef } from 'react';
import RecordingControls from './components/RecordingControls';
import ServiceSelector from './components/ServiceSelector';
import ModelSelector from './components/ModelSelector';
import LanguageSelector from './components/LanguageSelector';
import OptimizationModelSelector from './components/OptimizationModelSelector';
import CustomPrompt from './components/CustomPrompt';
import RecentTranscriptions from './components/RecentTranscriptions';
import { vscode } from './utilities/vscode';
import Switch from 'components/Switch';
import AudioSettings from './components/AudioSettings';
import Logo from './components/Logo';
import PromptModeSelector from 'components/PromptModeSelector';
import ApiKeyInput from 'components/ApiKeyInput';
// import ApiKeyInput from './components/ApiKeyInput';

// Add TypeScript declaration for the update timeout properties
declare global {
  interface Window {
    promptUpdateTimeout: NodeJS.Timeout | undefined;
    deviceUpdateTimeout: NodeJS.Timeout | undefined;
  }
}

interface Transcription {
  id: string;
  timestamp: string;
  originalText: string;
  optimizedText?: string;
  service: string;
  model: string;
  language: string;
}

// Track last update timestamps for each setting
interface LastUpdates {
  service: number;
  model: number;
  language: number;
  optimize: number;
  optimizationModel: number;
  translate: number;
  customPrompt: number;
  realtime: number;
  sampleRate: number;
  deviceId: number;
}

interface AudioDevice {
  id: string;
  name: string;
}

// Define state reference type
interface StateRef {
  service: string;
  model: string;
  language: string;
  optimizeEnabled: boolean;
  optimizationModel: string;
  translate: boolean;
  realtime: boolean;
  customPrompt: string;
  sampleRate: number;
  deviceId: string | null;
  apiKey: string;
}

const App: React.FC = () => {
  // Track last update timestamps
  const lastUpdates = useRef<LastUpdates>({
    service: 0,
    model: 0,
    language: 0,
    optimize: 0,
    optimizationModel: 0,
    translate: 0,
    customPrompt: 0,
    realtime: 0,
    sampleRate: 0,
    deviceId: 0
  });

  // Initialize state reference
  const stateRef = useRef<StateRef>({
    service: '',
    model: '',
    language: '',
    optimizeEnabled: false,
    optimizationModel: 'gpt-4o',
    translate: false,
    realtime: false,
    customPrompt: '',
    sampleRate: 44100,
    deviceId: null,
    apiKey: ''
  });

  // Flag to track initial configuration loading
  const [initialConfigLoaded, setInitialConfigLoaded] = useState<boolean>(false);

  // Recording state
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [isPaused, setIsPaused] = useState<boolean>(false);
  const [elapsedTime, setElapsedTime] = useState<number>(0);

  // Configuration state
  const [service, setService] = useState<string>('assemblyai');
  const [model, setModel] = useState<string>('whisper-1');
  const [lastAssemblyAIModel, setLastAssemblyAIModel] = useState<string>('best');
  const [language, setLanguage] = useState<string>('en');
  const [customPrompt, setCustomPrompt] = useState<string>('Correct any grammar issues and improve clarity. Keep the meaning intact.');
  const [optimizeEnabled, setOptimizeEnabled] = useState<boolean>(true);
  const [optimizationModel, setOptimizationModel] = useState<string>('gpt-4o');
  const [translate, setTranslate] = useState<boolean>(false);
  const [realtime, setRealtime] = useState<boolean>(false);

  // Audio settings state
  const [sampleRate, setSampleRate] = useState<number>(22050);
  const [deviceId, setDeviceId] = useState<string | null>(null);
  const [availableDevices, setAvailableDevices] = useState<AudioDevice[]>([]);

  // Add API key state
  const [apiKey, setApiKey] = useState<string>('');

  // Keep stateRef in sync with the actual state
  useEffect(() => {
    stateRef.current = {
      service,
      model,
      language,
      optimizeEnabled,
      optimizationModel,
      translate,
      realtime,
      customPrompt,
      sampleRate,
      deviceId,
      apiKey
    };
  }, [service, model, language, optimizeEnabled, optimizationModel, translate, realtime, customPrompt, customPrompt.length, sampleRate, deviceId, apiKey]);

  // We've removed the periodic polling for transcription updates
  // to prevent constant refreshing that causes janky UI behavior
  useEffect(() => {
    // Only request transcriptions once when initial config is loaded
    if (initialConfigLoaded) {
      console.log('WebView: Requesting initial transcriptions');
      vscode.postMessage({ command: 'getTranscriptions' });
    }
  }, [initialConfigLoaded]);

  // Transcriptions
  const [transcriptions, setTranscriptions] = useState<Transcription[]>([]);

  // Prevent updating state with older values from extension
  const updateStateIfNewer = useCallback((key: keyof LastUpdates, value: any, setter: React.Dispatch<React.SetStateAction<any>>) => {
    const now = Date.now();
    const lastUpdate = lastUpdates.current[key];

    // Always update if initial config hasn't been loaded yet
    if (!initialConfigLoaded) {
      lastUpdates.current[key] = now;
      setter(value);
      return true;
    }

    // If this is a newer update (or there's a threshold of 1000ms passed), apply it
    if (now - lastUpdate > 1000) {
      lastUpdates.current[key] = now;
      setter(value);
      return true;
    }
    return false;
  }, [initialConfigLoaded]);

  // Handle receiving messages from extension
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      console.log('WebView: Received message from extension:', message.command);

      switch (message.command) {
        case 'transcription':
          setTranscriptions((prev: Transcription[]) => [
            {
              id: Date.now().toString(),
              timestamp: new Date().toISOString(),
              originalText: message.text,
              optimizedText: message.optimizedText,
              // Use stateRef to access the current state values without dependency issues
              service: stateRef.current.service,
              model: stateRef.current.model,
              language: stateRef.current.language
            },
            ...prev
          ]);
          break;

        case 'initialConfiguration':
          if (message.options) {
            console.log('Received initial configuration:', message.options);

            const { service: newService, model: newModel, language: newLanguage,
              customPrompt: newPrompt, optimize: newOptimize, optimizationModel: newOptimizationModel, translate: newTranslate,
              realtime: newRealtime, apiKey: newApiKey, audioDevice: newAudioDevice } = message.options;

            // Set all initial values directly without checking timestamps
            if (newService !== undefined) {
              console.log(`Setting service to: ${newService} (was: ${service})`);
              setService(newService);
            }
            if (newModel !== undefined) {
              console.log(`Setting model to: ${newModel} (was: ${model})`);
              setModel(newModel);
            }
            if (newLanguage !== undefined) {
              console.log(`Setting language to: ${newLanguage} (was: ${language})`);
              setLanguage(newLanguage);
            }
            if (newPrompt !== undefined) {
              console.log(`Setting custom prompt (length: ${newPrompt.length})`);
              setCustomPrompt(newPrompt);
            }
            if (newOptimize !== undefined) {
              console.log(`Setting optimize to: ${newOptimize} (was: ${optimizeEnabled})`);
              setOptimizeEnabled(newOptimize);
            }
            if (newTranslate !== undefined) {
              console.log(`Setting translate to: ${newTranslate} (was: ${translate})`);
              setTranslate(newTranslate);
            }
            if (newRealtime !== undefined) {
              console.log(`Setting realtime to: ${newRealtime} (was: ${realtime})`);
              setRealtime(newRealtime);
            }
            if (newOptimizationModel !== undefined) {
              console.log(`Setting optimization model to: ${newOptimizationModel} (was: ${optimizationModel})`);
              setOptimizationModel(newOptimizationModel);
            }
            if (newApiKey !== undefined) {
              console.log('Setting API key');
              setApiKey(newApiKey);
            }
            if (newAudioDevice !== undefined) {
              console.log('Setting audio device:', newAudioDevice);
              setDeviceId(newAudioDevice);
            }

            // Save AssemblyAI model if appropriate
            if (newService === 'assemblyai' && newModel !== undefined) {
              setLastAssemblyAIModel(newModel);
            }

            // Mark initial config as loaded
            setInitialConfigLoaded(true);

            // Update the last update timestamps
            Object.keys(lastUpdates.current).forEach(key => {
              lastUpdates.current[key as keyof LastUpdates] = Date.now();
            });

            // Log the final state after all updates
            setTimeout(() => {
              console.log('WebView state after initialization:');
              console.log(`Service: ${service}`);
              console.log(`Model: ${model}`);
              console.log(`Language: ${language}`);
              console.log(`Optimize: ${optimizeEnabled}`);
              console.log(`Custom prompt length: ${customPrompt.length}`);
              console.log(`Translate: ${translate}`);
              console.log(`Realtime: ${realtime}`);
            }, 100);
          } else {
            console.warn('Received initialConfiguration message with no options');
          }
          break;

        case 'updateOptions':
          if (message.options) {
            console.log('Received updateOptions:', message.options);

            const { service: newService, model: newModel, language: newLanguage,
              customPrompt: newPrompt, optimize: newOptimize, optimizationModel: newOptimizationModel, translate: newTranslate,
              realtime: newRealtime, audioDevice: newAudioDevice } = message.options;

            // Track if any updates were applied
            let configUpdated = false;

            // Add more aggressive updates for certain cases
            const forceUpdate = !initialConfigLoaded || message.force === true;

            // Log current state for comparison
            console.log('Current state before updateOptions:');
            console.log(`Service: ${service}, Model: ${model}, Language: ${language}`);
            console.log(`Optimize: ${optimizeEnabled}, Translate: ${translate}, Realtime: ${realtime}`);

            // Only update if the incoming change is not a result of a recent local change
            // or if we're forcing the update
            if (newService !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.service > 1000)) {
                if (newService !== service) {
                  console.log(`Updating service from ${service} to ${newService}`);
                  setService(newService);
                  lastUpdates.current.service = Date.now();
                  configUpdated = true;
                }
              } else {
                console.log(`Skipping service update (${newService}) due to recent local update`);
              }
            }

            if (newModel !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.model > 1000)) {
                if (newModel !== model) {
                  console.log(`Updating model from ${model} to ${newModel}`);
                  setModel(newModel);
                  lastUpdates.current.model = Date.now();
                  configUpdated = true;

                  // Also update lastAssemblyAIModel if appropriate
                  if (newService === 'assemblyai' || service === 'assemblyai') {
                    setLastAssemblyAIModel(newModel);
                  }
                }
              } else {
                console.log(`Skipping model update (${newModel}) due to recent local update`);
              }
            }

            if (newLanguage !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.language > 1000)) {
                if (newLanguage !== language) {
                  console.log(`Updating language from ${language} to ${newLanguage}`);
                  setLanguage(newLanguage);
                  lastUpdates.current.language = Date.now();
                  configUpdated = true;
                }
              } else {
                console.log(`Skipping language update (${newLanguage}) due to recent local update`);
              }
            }

            if (newPrompt !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.customPrompt > 1000)) {
                console.log(`Updating custom prompt (length: ${newPrompt.length})`);
                setCustomPrompt(newPrompt);
                lastUpdates.current.customPrompt = Date.now();
                configUpdated = true;
              } else {
                console.log(`Skipping custom prompt update due to recent local update`);
              }
            }

            if (newOptimize !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.optimize > 1000)) {
                if (newOptimize !== optimizeEnabled) {
                  console.log(`Updating optimize from ${optimizeEnabled} to ${newOptimize}`);
                  setOptimizeEnabled(newOptimize);
                  lastUpdates.current.optimize = Date.now();
                  configUpdated = true;
                }
              } else {
                console.log(`Skipping optimize update (${newOptimize}) due to recent local update`);
              }
            }

            if (newOptimizationModel !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.optimizationModel > 1000)) {
                if (newOptimizationModel !== optimizationModel) {
                  console.log(`Updating optimization model from ${optimizationModel} to ${newOptimizationModel}`);
                  setOptimizationModel(newOptimizationModel);
                  lastUpdates.current.optimizationModel = Date.now();
                  configUpdated = true;
                }
              } else {
                console.log(`Skipping optimization model update (${newOptimizationModel}) due to recent local update`);
              }
            }

            if (newTranslate !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.translate > 1000)) {
                if (newTranslate !== translate) {
                  console.log(`Updating translate from ${translate} to ${newTranslate}`);
                  setTranslate(newTranslate);
                  lastUpdates.current.translate = Date.now();
                  configUpdated = true;
                }
              } else {
                console.log(`Skipping translate update (${newTranslate}) due to recent local update`);
              }
            }

            if (newRealtime !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.realtime > 1000)) {
                if (newRealtime !== realtime) {
                  console.log(`Updating realtime from ${realtime} to ${newRealtime}`);
                  setRealtime(newRealtime);
                  lastUpdates.current.realtime = Date.now();
                  configUpdated = true;
                }
              } else {
                console.log(`Skipping realtime update (${newRealtime}) due to recent local update`);
              }
            }

            if (newAudioDevice !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.deviceId > 1000)) {
                if (newAudioDevice !== deviceId) {
                  console.log(`Updating audio device from ${deviceId} to ${newAudioDevice}`);
                  setDeviceId(newAudioDevice);
                  lastUpdates.current.deviceId = Date.now();
                  configUpdated = true;
                }
              } else {
                console.log(`Skipping audio device update (${newAudioDevice}) due to recent local update`);
              }
            }

            // If any config was updated and this is the initial load, mark as loaded
            if (!initialConfigLoaded && configUpdated) {
              console.log('Initial configuration loaded via updateOptions');
              setInitialConfigLoaded(true);
            }

            // Log if anything was updated
            if (configUpdated) {
              console.log('Configuration updated successfully');
            } else {
              console.log('No configuration changes applied');
            }
          } else {
            console.warn('Received updateOptions message with no options');
          }
          break;

        case 'recordingState':
          console.log('[WebView] [DEBUG] Received recordingState message:', message);
          console.log('[WebView] [DEBUG] Current recording state before update:', { isRecording, isPaused, elapsedTime });

          if (message.isRecording !== undefined) {
            console.log(`[WebView] [DEBUG] Updating isRecording from ${isRecording} to ${message.isRecording}`);
            setIsRecording(message.isRecording);
          }
          if (message.isPaused !== undefined) {
            console.log(`[WebView] [DEBUG] Updating isPaused from ${isPaused} to ${message.isPaused}`);
            setIsPaused(message.isPaused);
          }
          if (message.elapsedTime !== undefined) {
            console.log(`[WebView] [DEBUG] Updating elapsedTime from ${elapsedTime} to ${message.elapsedTime}`);
            setElapsedTime(message.elapsedTime);
          }
          break;

        case 'updateTranscriptions':
          if (message.transcriptions) {
            console.log(`WebView: Received ${message.transcriptions.length} transcriptions from extension`);
            console.log('WebView: Current transcriptions count before update:', transcriptions.length);

            if (message.transcriptions.length === 0) {
              console.log('WebView: Received empty transcriptions array - this should clear the history');
            } else if (message.transcriptions.length > 0) {
              // Log some details about the most recent transcription for debugging
              const latest = message.transcriptions[0]; // transcriptions come in reverse order (newest first)
              console.log('WebView: Most recent transcription:', {
                id: latest.id,
                timestamp: latest.timestamp,
                originalTextLength: latest.originalText?.length || 0,
                hasOptimized: !!latest.optimizedText,
                service: latest.service
              });
            }

            setTranscriptions(message.transcriptions);
            console.log('WebView: Transcription state updated to', message.transcriptions.length, 'items');
          } else {
            console.warn('WebView: Received updateTranscriptions message with no transcriptions data');
          }
          break;

        case 'optimizationComplete':
          if (message.id && message.optimizedText) {
            setTranscriptions((prev) =>
              prev.map((t) =>
                t.id === message.id
                  ? { ...t, optimizedText: message.optimizedText }
                  : t
              )
            );
          }
          break;

        case 'transcriptionDeleted':
          if (message.id) {
            setTranscriptions((prev) => prev.filter((t) => t.id !== message.id));
          }
          break;

        case 'allTranscriptionsCleared':
          console.log('WebView: Received allTranscriptionsCleared message');
          console.log('WebView: Current transcriptions count before clearing:', transcriptions.length);
          setTranscriptions([]);
          console.log('WebView: Transcriptions cleared');
          break;

        case 'error':
          // Handle error messages from the extension
          console.error('Error from extension:', message.message);
          // You could show an error indicator in the UI here if needed
          break;

        case 'updateAudioDevices':
          if (message.devices) {
            setAvailableDevices(message.devices);
          }
          break;

        case 'updateAudioSettings':
          if (message.settings) {
            const { sampleRate: newRate, device: newDevice } = message.settings;

            // Only update the sample rate if it's related to real-time mode
            if (newRate === 16000 && realtime === true) {
              console.log('[WebView] Updating sample rate for real-time mode:', newRate);
              setSampleRate(newRate);
            } else if (newRate !== 16000 && realtime === false && previousSampleRate.current) {
              console.log('[WebView] Restoring previous sample rate after real-time mode:', previousSampleRate.current);
              setSampleRate(previousSampleRate.current);
            }

            // Only update the device ID during initial configuration
            if (newDevice !== undefined && !initialConfigLoaded) {
              console.log('[WebView] Initial setting of device ID from backend:', newDevice);
              setDeviceId(newDevice);
            }
          }
          break;
      }
    };

    window.addEventListener('message', handleMessage);

    // Request initial state from extension
    vscode.postMessage({ command: 'getOptions' });
    vscode.postMessage({ command: 'getTranscriptions' });

    // Request audio settings
    vscode.postMessage({ command: 'getAudioSettings' });

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [updateStateIfNewer, initialConfigLoaded, service, model, language, optimizeEnabled, optimizationModel, translate, realtime, customPrompt, customPrompt.length, sampleRate, deviceId, apiKey, transcriptions.length, isRecording, isPaused, elapsedTime]);

  // Memoized handlers to avoid recreating functions on every render
  const handleStartRecording = useCallback((): void => {
    console.log('[WebView] [DEBUG] Start recording called, current state:', { isRecording, isPaused });
    vscode.postMessage({
      command: 'startRecording',
      options: {
        service,
        model,
        language,
        optimize: optimizeEnabled,
        optimizationModel: optimizeEnabled ? optimizationModel : '',
        customPrompt: optimizeEnabled ? customPrompt : '',
        translate,
        realtime
      }
    });
    // Optimistically update UI state
    console.log('[WebView] [DEBUG] Optimistically updating UI state to recording');
    setIsRecording(true);
    setIsPaused(false);
  }, [service, model, language, optimizeEnabled, optimizationModel, customPrompt, translate, realtime, isRecording, isPaused]);

  const handleStopRecording = useCallback((): void => {
    console.log('[WebView] [DEBUG] Stop recording called, current state:', { isRecording, isPaused });
    vscode.postMessage({ command: 'stopRecording' });
    // Optimistically update UI state
    console.log('[WebView] [DEBUG] Optimistically updating UI state to stopped');
    setIsRecording(false);
    setIsPaused(false);
  }, [isRecording, isPaused]);

  const handlePauseRecording = useCallback((): void => {
    console.log('[WebView] [DEBUG] Pause recording called, current state:', { isRecording, isPaused });
    vscode.postMessage({ command: 'pauseRecording' });
    // Optimistically update UI state
    console.log('[WebView] [DEBUG] Optimistically updating UI state to paused');
    setIsPaused(true);
  }, [isRecording, isPaused]);

  const handleResumeRecording = useCallback((): void => {
    console.log('[WebView] [DEBUG] Resume recording called, current state:', { isRecording, isPaused });
    vscode.postMessage({ command: 'resumeRecording' });
    // Optimistically update UI state
    console.log('[WebView] [DEBUG] Optimistically updating UI state to resumed');
    setIsPaused(false);
  }, [isRecording, isPaused]);

  const handleCancelRecording = useCallback((): void => {
    console.log('[WebView] [DEBUG] Cancel recording called, current state:', { isRecording, isPaused });
    vscode.postMessage({ command: 'cancelRecording' });
    // Optimistically update UI state
    console.log('[WebView] [DEBUG] Optimistically updating UI state to cancelled');
    setIsRecording(false);
    setIsPaused(false);
  }, [isRecording, isPaused]);

  // Handlers for configuration changes
  const handleServiceChange = useCallback((value: string): void => {
    // Record the timestamp of this update
    lastUpdates.current.service = Date.now();

    console.log(`WebView: Service change requested from ${service} to ${value}`);
    console.log(`WebView: Current model before change: ${model}`);
    console.log(`WebView: Current language before change: ${language}`);

    // Automatically set the appropriate model based on service
    let newModel = model;
    let newLanguage = language;
    let updateLanguage = false;

    if (value === 'openai') {
      // Save current AssemblyAI model if we're switching from it
      if (service === 'assemblyai') {
        console.log(`WebView: Saving AssemblyAI model ${model} for future use`);
        setLastAssemblyAIModel(model);
      }
      newModel = 'whisper-1';
      console.log(`WebView: Setting model to ${newModel} for OpenAI service`);

      // Record model change timestamp
      lastUpdates.current.model = Date.now();

      // For OpenAI, we accept many languages - no need to update
    } else if (value === 'assemblyai') {
      // Restore the last used AssemblyAI model
      newModel = lastAssemblyAIModel;
      console.log(`WebView: Restoring AssemblyAI model to ${newModel}`);

      // Record model change timestamp
      lastUpdates.current.model = Date.now();

      // Check language compatibility for AssemblyAI - set to a limited subset of the most common ones
      // The backend will handle the full validation against the actual model-specific lists
      const commonAssemblyAILanguages = ['en', 'es', 'fr', 'de', 'it', 'pt', 'nl', 'hi', 'ja', 'zh', 'ar'];
      if (language === 'auto' || !commonAssemblyAILanguages.includes(language)) {
        console.log(`WebView: Language ${language} not supported by AssemblyAI, changing to en`);
        newLanguage = 'en';
        updateLanguage = true;

        // Record language change timestamp
        lastUpdates.current.language = Date.now();
      }

      // If switching to AssemblyAI, disable translate
      if (translate) {
        console.log(`WebView: Disabling translate for AssemblyAI service`);
        // Record translate change timestamp
        lastUpdates.current.translate = Date.now();
        setTranslate(false);
      }
    }

    // Update local state immediately
    setService(value);
    setModel(newModel);

    // Update language if needed
    if (updateLanguage) {
      setLanguage(newLanguage);
    }

    // Notify VS Code of the changes
    vscode.postMessage({
      command: 'updateOptions',
      options: {
        service: value,
        model: newModel,
        ...(updateLanguage ? { language: newLanguage } : {}),
        ...(value === 'assemblyai' && translate ? { translate: false } : {})
      }
    });

    console.log(`WebView: Service changed to ${value}, model set to ${newModel}, language: ${updateLanguage ? newLanguage : 'unchanged'}`);
  }, [service, model, language, lastAssemblyAIModel, translate]);

  const handleModelChange = useCallback((value: string): void => {
    // Record the timestamp of this update
    lastUpdates.current.model = Date.now();

    // Update state immediately
    setModel(value);

    // Notify VS Code of the changes
    vscode.postMessage({
      command: 'updateOptions',
      options: { model: value }
    });
  }, []);

  const handleLanguageChange = useCallback((value: string): void => {
    // Record the timestamp of this update
    lastUpdates.current.language = Date.now();

    // Update state immediately
    setLanguage(value);

    // Notify VS Code of the changes
    vscode.postMessage({
      command: 'updateOptions',
      options: { language: value }
    });
  }, []);

  // Debounced version of handlePromptChange to prevent too many updates
  const handlePromptChange = useCallback((value: string): void => {
    // Record the timestamp of this update
    lastUpdates.current.customPrompt = Date.now();

    // Update state immediately
    setCustomPrompt(value);

    // Use a debounce mechanism to avoid sending too many updates
    // Clear any existing timeout
    if (window.promptUpdateTimeout) {
      clearTimeout(window.promptUpdateTimeout);
    }

    // Set a new timeout to update after 500ms of inactivity
    window.promptUpdateTimeout = setTimeout(() => {
      // Notify VS Code of the changes
      vscode.postMessage({
        command: 'updateOptions',
        options: { customPrompt: value }
      });
      console.log(`Custom prompt updated (debounced): ${value.substring(0, 30)}${value.length > 30 ? '...' : ''}`);
    }, 500);
  }, []);

  // Add cleanup for the timeout
  useEffect(() => {
    return () => {
      if (window.promptUpdateTimeout) {
        clearTimeout(window.promptUpdateTimeout);
      }
    };
  }, []);

  const handleOptimizeToggle = useCallback((value: boolean): void => {
    // Record the timestamp of this update
    lastUpdates.current.optimize = Date.now();

    // Update state immediately
    setOptimizeEnabled(value);

    // Notify VS Code of the changes
    vscode.postMessage({
      command: 'updateOptions',
      options: { optimize: value }
    });
  }, []);

  const handleTranslateToggle = useCallback((checked: boolean): void => {
    // Don't update if we're still loading initial config
    if (!initialConfigLoaded) return;

    setTranslate(checked);
    lastUpdates.current.translate = Date.now();
    vscode.postMessage({
      command: 'updateOptions',
      options: { translate: checked }
    });
  }, [initialConfigLoaded]);

  // Store the previous sample rate when toggling real-time
  const previousSampleRate = useRef<number>(0);

  const handleRealtimeToggle = useCallback((checked: boolean): void => {
    // Don't update if we're still loading initial config
    if (!initialConfigLoaded) return;

    console.log('[WebView] Toggling real-time transcription:', checked);

    // Update realtime state immediately
    setRealtime(checked);

    // Optimistically update the sample rate UI
    if (checked) {
      // Store current sample rate before switching to real-time
      previousSampleRate.current = sampleRate;
      console.log('[WebView] Storing current sample rate before real-time:', previousSampleRate.current);

      // Immediately set to 16000Hz for real-time mode
      setSampleRate(16000);
    } else if (previousSampleRate.current) {
      // Immediately restore previous sample rate when disabling real-time
      console.log('[WebView] Restoring sample rate after real-time:', previousSampleRate.current);
      setSampleRate(previousSampleRate.current);
    }

    // Notify backend
    vscode.postMessage({
      command: 'updateOptions',
      options: { realtime: checked }
    });
  }, [initialConfigLoaded, sampleRate]);

  const handleOptimizationModelChange = useCallback((value: string): void => {
    // Record the timestamp of this update
    lastUpdates.current.optimizationModel = Date.now();

    // Update state immediately
    setOptimizationModel(value);

    // Notify VS Code of the changes
    vscode.postMessage({
      command: 'updateOptions',
      options: { optimizationModel: value }
    });

    console.log(`WebView: Optimization model changed to ${value}`);
  }, []);

  // Handlers for transcriptions
  const handleCopyOriginal = useCallback((id: string): void => {
    const transcription = transcriptions.find((t: Transcription) => t.id === id);
    if (transcription) {
      vscode.postMessage({
        command: 'copyToClipboard',
        text: transcription.originalText
      });
    }
  }, [transcriptions]);

  const handleCopyOptimized = useCallback((id: string): void => {
    const transcription = transcriptions.find((t: Transcription) => t.id === id);
    if (transcription && transcription.optimizedText) {
      vscode.postMessage({
        command: 'copyToClipboard',
        text: transcription.optimizedText
      });
    }
  }, [transcriptions]);

  const handleOptimize = useCallback((id: string): void => {
    const transcription = transcriptions.find((t: Transcription) => t.id === id);
    if (transcription) {
      vscode.postMessage({
        command: 'optimizeTranscription',
        id: id,
        text: transcription.originalText,
        customPrompt: customPrompt,
        optimizationModel: optimizationModel
      });

      // Optimistically show a loading state
      setTranscriptions(prev =>
        prev.map(t =>
          t.id === id
            ? { ...t, optimizedText: 'Optimizing...' }
            : t
        )
      );
    }
  }, [transcriptions, customPrompt, optimizationModel]);

  // Audio settings handlers
  const handleSampleRateChange = useCallback((newRate: number) => {
    console.log('[WebView] Changing sample rate to:', newRate);

    // Update local state immediately (optimistic update)
    setSampleRate(newRate);

    // Notify backend
    vscode.postMessage({
      command: 'updateSetting',
      key: 'voicehype.audio.sampleRate',
      value: newRate
    });
  }, []);

  const handleDeviceChange = useCallback((deviceId: string | null): void => {
    // Update local state first
    setDeviceId(deviceId);

    // Update timestamp to prevent backend update from overriding
    lastUpdates.current.deviceId = Date.now();

    // Use a debounce mechanism to avoid sending too many updates
    // Clear any existing timeout
    if (window.deviceUpdateTimeout) {
      clearTimeout(window.deviceUpdateTimeout);
    }

    // Set a new timeout to update after 500ms of inactivity
    window.deviceUpdateTimeout = setTimeout(() => {
      // Convert null to empty string for the backend
      const valueToSend = deviceId === null ? '' : deviceId;

      // Notify backend using the correct command for audio device changes
      vscode.postMessage({
        command: 'updateAudioDevice',
        deviceId: valueToSend
      });

      // Log the device ID for debugging
      console.log(`[WebView] Sending audio device change to backend (debounced): ${valueToSend}`);
    }, 500);
  }, []);

  // Add cleanup for the device update timeout
  useEffect(() => {
    return () => {
      if (window.deviceUpdateTimeout) {
        clearTimeout(window.deviceUpdateTimeout);
      }
    };
  }, []);

  return (
    <div className="sm:px-0 flex flex-col h-full px-1">
      {/* VoiceHype Logo Header */}
      <Logo className="py-4 mb-2" />

      {/* <ApiKeyInput initialApiKey={apiKey} /> */}

      {/* Recording controls */}
      <div className="mb-5">
        <RecordingControls
          isRecording={isRecording}
          isPaused={isPaused}
          elapsedTime={elapsedTime}
          onStart={handleStartRecording}
          onStop={handleStopRecording}
          onPause={handlePauseRecording}
          onResume={handleResumeRecording}
          onCancel={handleCancelRecording}
        />
      </div>

      {/* Service and model settings */}
      <div className="mb-4 space-y-4">
        <ServiceSelector
          service={service}
          onChange={handleServiceChange}
        />

        <ModelSelector
          service={service}
          model={model}
          onChange={handleModelChange}
        />

        <LanguageSelector
          service={service}
          model={model}
          language={language}
          onChange={handleLanguageChange}
        />
      </div>

      {/* Audio Settings  */}
      <div className="mb-4">
        <AudioSettings
          sampleRate={sampleRate}
          deviceId={deviceId}
          availableDevices={availableDevices}
          onSampleRateChange={handleSampleRateChange}
          onDeviceChange={handleDeviceChange}
        />
      </div>

      {/* Optimization toggle (already present in RecordingControls) */}
      {/* Organized next to each other for related functionality */}
      <div className="mb-4 space-y-2">
        {/* Translate toggle - only show when OpenAI is selected */}
        {service === 'openai' && (
          <Switch
            checked={translate}
            onChange={handleTranslateToggle}
            label="Translate to English"
          />
        )}

        {/* Real-time toggle - only show for AssemblyAI best model */}
        {service === 'assemblyai' && model === 'best' && (
          <Switch
            checked={realtime}
            onChange={handleRealtimeToggle}
            label="Use real-time transcription"
          />
        )}

        {/* Optimize toggle - moved from RecordingControls */}
        <Switch
          checked={optimizeEnabled}
          onChange={handleOptimizeToggle}
          label="Optimize transcription"
        />
      </div>

      {/* Optimization Mode & Model Selector */}
      {optimizeEnabled && (
        <div className="mb-4 space-y-4">
          <div>
            <h2 className="text-md mb-2 font-medium">Optimization Mode</h2>
            <PromptModeSelector
              value={customPrompt}
              onChange={handlePromptChange}
              onModeChange={(modeId) => {
                // Store the active mode ID if needed
                vscode.postMessage({
                  command: 'setActivePromptMode',
                  modeId
                });
              }}
            />
          </div>
          <div>
            <h2 className="text-md mb-2 font-medium">Optimization Model</h2>
            <OptimizationModelSelector
              optimizationModel={optimizationModel}
              onChange={handleOptimizationModelChange}
            />
          </div>
        </div>
      )}

      {/* Optimization Prompt - now handled inside PromptModeSelector */}


      {/* Transcription History */}
      <div className="flex-grow overflow-hidden">
        <h2 className="text-md mb-2 font-medium">Transcription History</h2>
        <div className="h-[calc(100%-1.75rem)]">
          <RecentTranscriptions
            transcriptions={transcriptions}
            onCopyOriginal={handleCopyOriginal}
            onCopyOptimized={handleCopyOptimized}
            onOptimize={handleOptimize}
          />
        </div>
      </div>
    </div>
  );
};

export default App;