---
title: Streaming
hide-nav-links: true
description: Transcribe live audio with Streaming Speech-to-Text
---

AssemblyAI's Streaming Speech-to-Text (STT) allows you to transcribe live audio streams with high accuracy and low latency. By streaming your audio data to our secure WebSocket API, you can receive transcripts back within a few hundred milliseconds.

<Note title="Supported languages">
  Streaming Speech-to-Text is only available for English.
</Note>

## Getting started

Check out our getting started guides:

<Info title="Getting Started Guides">

- [Python](/docs/getting-started/transcribe-streaming-audio-from-a-microphone/python)
- [JavaScript / TypeScript](/docs/getting-started/transcribe-streaming-audio-from-a-microphone/typescript)
{/* - [C#](/docs/getting-started/transcribe-streaming-audio-from-a-microphone/csharp) */}
{/* - [Ruby](/docs/getting-started/transcribe-streaming-audio-from-a-microphone/ruby)  */}

</Info>

If your programming language isn't supported yet, see the WebSocket API:

- [Streaming API reference](https://assemblyai.com/docs/api-reference/streaming)

## Audio requirements

The audio format must conform to the following requirements:

- PCM16 or Mu-law encoding (See [Specify the encoding](#specify-the-encoding))
- A sample rate that matches the value of the supplied `sample_rate` parameter
- Single-channel
- 100 to 2000 milliseconds of audio per message

<Tip>
  Audio segments with a duration between 100 ms and 450 ms produce the best
  results in transcription accuracy.
</Tip>

## Specify the encoding

<Tabs groupId="language">
<Tab language="python-sdk" title="Python SDK" default>

By default, transcriptions expect [PCM16 encoding](http://trac.ffmpeg.org/wiki/audio%20types). If you want to use [Mu-law encoding](http://trac.ffmpeg.org/wiki/audio%20types), you must set the `encoding` parameter to `aai.AudioEncoding.pcm_mulaw`:

```python {3}
transcriber = aai.RealtimeTranscriber(
    ...,
    encoding=aai.AudioEncoding.pcm_mulaw
)
```

</Tab>
<Tab language="python" title="Python">

By default, transcriptions expect [PCM16 encoding](http://trac.ffmpeg.org/wiki/audio%20types). If you want to use [Mu-law encoding](http://trac.ffmpeg.org/wiki/audio%20types), you must set the `encoding` parameter to `pcm_mulaw`:

```python {2}
ws = websocket.WebSocketApp(
    f'wss://api.assemblyai.com/v2/realtime/ws?sample_rate={SAMPLE_RATE}&encoding=pcm_mulaw',
    ...,
)
```

</Tab>
<Tab language="typescript-sdk" title="TypeScript SDK">

By default, transcriptions expect [PCM16 encoding](http://trac.ffmpeg.org/wiki/audio%20types). If you want to use [Mu-law encoding](http://trac.ffmpeg.org/wiki/audio%20types), you must set the `encoding` parameter to `'pcm_mulaw'`:

```ts {3}
const rt = client.realtime.transcriber({
  ...,
  encoding: 'pcm_mulaw'
})
```

</Tab>
<Tab language="typescript" title="TypeScript">

By default, transcriptions expect [PCM16 encoding](http://trac.ffmpeg.org/wiki/audio%20types). If you want to use [Mu-law encoding](http://trac.ffmpeg.org/wiki/audio%20types), you must set the `encoding` parameter to `pcm_mulaw`:

```ts {2}
const ws = new WebSocket(
    `wss://api.assemblyai.com/v2/realtime/ws?sample_rate=${SAMPLE_RATE}&encoding=pcm_mulaw`,
    ...,
  );
```

</Tab>
<Tab language="csharp" title="C#">

By default, transcriptions expect [PCM16 encoding](http://trac.ffmpeg.org/wiki/audio%20types). If you want to use [Mu-law encoding](http://trac.ffmpeg.org/wiki/audio%20types), you must set the `encoding` parameter to `pcm_mulaw`:

```csharp {1}
string url = $"wss://api.assemblyai.com/v2/realtime/ws?sample_rate={SAMPLE_RATE}&encoding=pcm_mulaw";
await ws.ConnectAsync(new Uri(url), cts.Token);
```

</Tab>
<Tab language="ruby" title="Ruby">

By default, transcriptions expect [PCM16 encoding](http://trac.ffmpeg.org/wiki/audio%20types). If you want to use [Mu-law encoding](http://trac.ffmpeg.org/wiki/audio%20types), you must set the `encoding` parameter to `pcm_mulaw`:

```ruby {2}
ws = WebSocket::Client::Simple.connect(
  "wss://api.assemblyai.com/v2/realtime/ws?sample_rate=#{SAMPLE_RATE}&encoding=pcm_mulaw",
  ...
)
```

</Tab>
</Tabs>

<Tabs groupId="language">
<Tab language="python-sdk" title="Python SDK">

| Encoding            | SDK Parameter                 | Description                      |
| ------------------- | ----------------------------- | -------------------------------- |
| **PCM16** (default) | `aai.AudioEncoding.pcm_s16le` | PCM signed 16-bit little-endian. |
| **Mu-law**          | `aai.AudioEncoding.pcm_mulaw` | PCM Mu-law.                      |

</Tab>
<Tab language="python" title="Python">

| Encoding            | SDK Parameter | Description                      |
| ------------------- | ------------- | -------------------------------- |
| **PCM16** (default) | `'pcm_s16le'` | PCM signed 16-bit little-endian. |
| **Mu-law**          | `'pcm_mulaw'` | PCM Mu-law.                      |

</Tab>
<Tab language="typescript-sdk" title="TypeScript SDK">

| Encoding            | SDK Parameter | Description                      |
| ------------------- | ------------- | -------------------------------- |
| **PCM16** (default) | `'pcm_s16le'` | PCM signed 16-bit little-endian. |
| **Mu-law**          | `'pcm_mulaw'` | PCM Mu-law.                      |

</Tab>
<Tab language="typescript" title="TypeScript">

| Encoding            | SDK Parameter | Description                      |
| ------------------- | ------------- | -------------------------------- |
| **PCM16** (default) | `'pcm_s16le'` | PCM signed 16-bit little-endian. |
| **Mu-law**          | `'pcm_mulaw'` | PCM Mu-law.                      |

</Tab>
<Tab language="csharp" title="C#">

| Encoding            | SDK Parameter | Description                      |
| ------------------- | ------------- | -------------------------------- |
| **PCM16** (default) | `'pcm_s16le'` | PCM signed 16-bit little-endian. |
| **Mu-law**          | `'pcm_mulaw'` | PCM Mu-law.                      |

</Tab>
<Tab language="ruby" title="Ruby">

| Encoding            | SDK Parameter | Description                      |
| ------------------- | ------------- | -------------------------------- |
| **PCM16** (default) | `'pcm_s16le'` | PCM signed 16-bit little-endian. |
| **Mu-law**          | `'pcm_mulaw'` | PCM Mu-law.                      |

</Tab>
</Tabs>

## Add custom vocabulary

You can add up to 2500 characters of custom vocabulary to boost their transcription probability.

<Tabs groupId="language">
<Tab language="python-sdk" title="Python SDK" default>

For this, create a list of strings and set the `word_boost` parameter:

```python {3}
transcriber = aai.RealtimeTranscriber(
    ...,
    word_boost=["aws", "azure", "google cloud"]
)
```

</Tab>
<Tab language="python" title="Python">

For this, create a list of strings, URL encode them and add them to the URL:

```python {2,4}
list = ["aws", "azure", "google cloud"]
word_boost = urllib.parse.quote(json.dumps(list))
ws = websocket.WebSocketApp(
    f'wss://api.assemblyai.com/v2/realtime/ws?sample_rate={SAMPLE_RATE}&encoding=pcm_mulaw&word_boost={word_boost}',
    ...,
)
```

</Tab>
<Tab language="typescript-sdk" title="TypeScript SDK">
For this, create a list of strings and set the `wordBoost` parameter:

```ts {3}
const rt = client.realtime.transcriber({
  ...,
  wordBoost:['aws', 'azure', 'google cloud']
})
```

</Tab>
<Tab language="typescript" title="TypeScript">

For this, create a list of strings, URL encode them and add them to the URL:

```ts {2,4}
const list = ["aws", "azure", "google cloud"];
const wordBoost = encodeURIComponent(JSON.stringify(list));
const ws = new WebSocket(
    `wss://api.assemblyai.com/v2/realtime/ws?sample_rate=${SAMPLE_RATE}&word_boost=${wordBoost}`,
    ...,
  );
```

</Tab>
<Tab language="csharp" title="C#">
For this, create a list of strings, URL encode them and add them to the URL:

```csharp {2,4}
string[] list = { "aws", "azure", "google cloud" };
string wordBoost = Uri.EscapeDataString(JsonSerializer.Serialize(list));

string url = $"wss://api.assemblyai.com/v2/realtime/ws?sample_rate={SAMPLE_RATE}&word_boost={wordBoost}";
await ws.ConnectAsync(new Uri(url), cts.Token);
```

</Tab>
<Tab language="ruby" title="Ruby">

For this, create a list of strings, URL encode them and add them to the URL:

```ruby {2, 5}
list = ["aws", "azure", "google cloud"]
word_boost = URI.encode_www_form_component(JSON.dump(list))

ws = WebSocket::Client::Simple.connect(
  "wss://api.assemblyai.com/v2/realtime/ws?sample_rate=#{SAMPLE_RATE}&word_boost=#{word_boost}",
  ...
)
```

</Tab>
</Tabs>

<Note>
  If you're not using one of the SDKs, you must ensure that the `word_boost`
  parameter is a JSON array that is URL encoded. See this [code
  example](/docs/guides/real-time-streaming-transcription#adding-custom-vocabulary).
</Note>

## Authenticate with a temporary token

If you need to authenticate on the client, you can avoid exposing your API key by using temporary authentication tokens.
You should generate this token on your server and pass it to the client.

<Steps>
<Step>

<Tabs groupId="language">
<Tab language="python-sdk" title="Python SDK" default>
To generate a temporary token, call `aai.RealtimeTranscriber.create_temporary_token()`.

Use the `expires_in` parameter to specify the session duration in seconds for which the token will remain valid.

```python
token = aai.RealtimeTranscriber.create_temporary_token(
    expires_in=60
)
```

</Tab>
<Tab language="python" title="Python">

To generate a temporary token, make a POST request to the temporary token endpoint.

Use the `expires_in` parameter to specify the session duration in seconds for which the token will remain valid.

```python
token = requests.post(
    'https://api.assemblyai.com/v2/realtime/token',
    headers={
        'Authorization': '<apiKey>',
        'Content-Type': 'application/json'
    },
    json={'expires_in': 60}
).json()['token']
```

</Tab>
<Tab language="typescript-sdk" title="TypeScript SDK">

To generate a temporary token, call `client.realtime.createTemporaryToken()`.

Use the `expires_in` parameter to specify the session duration in seconds for which the token will remain valid.

```ts
const token = await client.realtime.createTemporaryToken({ expires_in: 60 });
```

</Tab>
<Tab language="typescript" title="TypeScript">

To generate a temporary token, make a POST request to the temporary token endpoint.

Use the `expires_in` parameter to specify the session duration in seconds for which the token will remain valid.

```ts
const token = await axios
  .post(
    "https://api.assemblyai.com/v2/realtime/token",
    { expires_in: 60 },
    {
      headers: {
        Authorization: "<apiKey>",
        "Content-Type": "application/json",
      },
    }
  )
  .then((response) => response.data.token);
```

</Tab>
<Tab language="csharp" title="C#">

To generate a temporary token, make a POST request to the temporary token endpoint.

Use the `expires_in` parameter to specify the session duration in seconds for which the token will remain valid.

```csharp
var response = (await client.PostAsJsonAsync("https://api.assemblyai.com/v2/realtime/token",
    new { expires_in = 60 })
).Content.ReadAsStringAsync().Result;
var token = JsonConvert.DeserializeObject<dynamic>(response).token;
```

</Tab>
<Tab language="ruby" title="Ruby">

To generate a temporary token, make a POST request to the temporary token endpoint.

Use the `expires_in` parameter to specify the session duration in seconds for which the token will remain valid.

```ruby
uri = URI('https://api.assemblyai.com/v2/realtime/token')
request = Net::HTTP::Post.new(uri)
request['Authorization'] = '<apiKey>'
request['Content-Type'] = 'application/json'
request.body = { expires_in: 60 }.to_json

response = Net::HTTP.start(uri.hostname, uri.port, use_ssl: true) { |http| http.request(request) }

token = JSON.parse(response.body)['token']
```

</Tab>
<Tab language="curl" title="cURL">

To generate a temporary token, make a POST request to the temporary token endpoint.

Use the `expires_in` parameter to specify the session duration in seconds for which the token will remain valid.

```
curl -X POST https://api.assemblyai.com/v2/realtime/token \
     -H "Authorization: <apiKey>" \
     -H "Content-Type: application/json" \
     -d '{
  "expires_in": 60
}'
```

</Tab>
</Tabs>

<Note>The expiration time must be a value between 60 and 360000 seconds.</Note>

</Step>
<Step>

The client should retrieve the token from the server and use the token to authenticate the transcriber.

<Note>
  Each token has a one-time use restriction and can only be used for a single
  session. Any usage associated with a temporary token will be attributed to the
  API key that generated it.
</Note>

<Tabs groupId="language">
<Tab language="python-sdk" title="Python SDK" default>
  
To use it, specify the `token` parameter when initializing the streaming transcriber.

```python {3}
transcriber = aai.RealtimeTranscriber(
    ...,
    token=token
)
```

</Tab>
<Tab language="python" title="Python">
  
To use it, specify the `token` parameter as a query parameter in the WebSocket URL.

```python {2}
ws = websocket.WebSocketApp(
    f'wss://api.assemblyai.com/v2/realtime/ws?sample_rate={SAMPLE_RATE}&token={token}',
    ...,
)
```

</Tab>
<Tab language="typescript-sdk" title="TypeScript SDK">
  
To use it, specify the `token` parameter when initializing the streaming transcriber.

```ts {3}
const rt = new RealtimeTranscriber({
  ...,
  token
})
```

</Tab>
<Tab language="typescript" title="TypeScript">
  
To use it, specify the `token` parameter as a query parameter in the WebSocket URL.

```ts {2}
const ws = new WebSocket(
    `wss://api.assemblyai.com/v2/realtime/ws?sample_rate=${SAMPLE_RATE}&token=${token}`,
    ...,
  );
```

</Tab>
<Tab language="csharp" title="C#">

To use it, specify the `token` parameter as a query parameter in the WebSocket URL.

```csharp {1}
string url = $"wss://api.assemblyai.com/v2/realtime/ws?sample_rate={SAMPLE_RATE}&token={token}";
await ws.ConnectAsync(new Uri(url), cts.Token);
```

</Tab>
<Tab language="ruby" title="Ruby">

To use it, specify the `token` parameter as a query parameter in the WebSocket URL.

```ruby {2}
ws = WebSocket::Client::Simple.connect(
  "wss://api.assemblyai.com/v2/realtime/ws?sample_rate=#{SAMPLE_RATE}&token=#{token}",
  ...
)
```

</Tab>
</Tabs>

</Step>
</Steps>

## Manually end current utterance

<Tabs groupId="language">
<Tab language="python-sdk" title="Python SDK" default>
To manually end an utterance, call `force_end_utterance()`:

```python
transcriber.force_end_utterance()
```

</Tab>
<Tab language="python" title="Python">
To manually end an utterance, send the following message:

```python
message = {"force_end_utterance": True}
ws.send(json.dumps(message))
```

</Tab>
<Tab language="typescript-sdk" title="TypeScript SDK">
To manually end an utterance, call `forceEndUtterance()`:

```ts
rt.forceEndUtterance();
```

</Tab>
<Tab language="typescript" title="TypeScript">
To manually end an utterance, send the following message:

```ts
const message = JSON.stringify({ force_end_utterance: true });
ws.send(message);
```

</Tab>
<Tab language="csharp" title="C#">
To manually end an utterance, send the following message:

```csharp
await ws.SendAsync(
    new ArraySegment<byte>(Encoding.UTF8.GetBytes("{\"force_end_utterance\": true}")),
    WebSocketMessageType.Text,
    true,
    CancellationToken.None
);
```

</Tab>
<Tab language="ruby" title="Ruby">
To manually end an utterance, send the following message:

```ruby
ws.send('{"force_end_utterance": true}')
```

</Tab>
</Tabs>

Manually ending an utterance immediately produces a final transcript.

## Configure the threshold for automatic utterance detection

You can configure the threshold for how long to wait before ending an utterance.

<Tabs groupId="language">
<Tab language="python-sdk" title="Python SDK" default>
To change the threshold, you can specify the `end_utterance_silence_threshold` parameter when initializing the streaming transcriber.

After the session has started, you can change the threshold by calling `configure_end_utterance_silence_threshold()`.

```python {3,7}
transcriber = aai.RealtimeTranscriber(
    ...,
    end_utterance_silence_threshold=500
)

# after connecting
transcriber.configure_end_utterance_silence_threshold(300)
```

</Tab>
<Tab language="python" title="Python">

After the session has started, you can change the threshold by sending the following message:

```python {2}
message = {"end_utterance_silence_threshold": 300}
ws.send(json.dumps(message))
```

</Tab>
<Tab language="typescript-sdk" title="TypeScript SDK">
To change the threshold, you can specify the `endUtteranceSilenceThreshold` parameter when initializing the streaming transcriber.

After the session has started, you can change the threshold by calling `configureEndUtteranceSilenceThreshold()`.

```ts {3,8}
const rt = client.realtime.transcriber({
  ...,
  endUtteranceSilenceThreshold: 500
})

// after connecting
rt.configureEndUtteranceSilenceThreshold(300)
```

</Tab>
<Tab language="typescript" title="TypeScript">

After the session has started, you can change the threshold by sending the following message:

```ts {2}
const message = JSON.stringify({ end_utterance_silence_threshold: 300 });
ws.send(message);
```

</Tab>

<Tab language="csharp" title="C#">
After the session has started, you can change the threshold by sending the following message:

```csharp
await ws.SendAsync(
    new ArraySegment<byte>(Encoding.UTF8.GetBytes("{\"end_utterance_silence_threshold\": 300}")),
    WebSocketMessageType.Text,
    true,
    CancellationToken.None
);
```

</Tab>
<Tab language="ruby" title="Ruby">

After the session has started, you can change the threshold by sending the following message:

```ruby
ws.send('{"end_utterance_silence_threshold": 300}')
```

</Tab>
</Tabs>

<Note>
  By default, Streaming Speech-to-Text ends an utterance after 700 milliseconds
  of silence. You can configure the duration threshold any number of times
  during a session after the session has started. The valid range is between 0
  and 20000.
</Note>

## Disable partial transcripts

If you're only using the final transcript, you can disable partial transcripts to reduce network traffic.

<Tabs groupId="language">
<Tab language="python-sdk" title="Python SDK" default>
To disable partial transcripts, set the `disable_partial_transcripts` parameter to `True`.

```python {3}
transcriber = aai.RealtimeTranscriber(
    ...,
    disable_partial_transcripts=True
)
```

</Tab>
<Tab language="python" title="Python">
To disable partial transcripts, set the `disable_partial_transcripts` parameter to `true`.

```python {2}
ws = websocket.WebSocketApp(
    f'wss://api.assemblyai.com/v2/realtime/ws?sample_rate={SAMPLE_RATE}&disable_partial_transcripts=true',
    ...,
)
```

</Tab>
<Tab language="typescript-sdk" title="TypeScript SDK">
To disable partial transcripts, set the `disablePartialTranscripts` parameter to `true`.

```ts {3}
const rt = client.realtime.transcriber({
  ...,
  disablePartialTranscripts: true
})
```

</Tab>
<Tab language="typescript" title="TypeScript">
To disable partial transcripts, set the `disable_partial_transcripts` parameter to `true`.

```ts {2}
const ws = new WebSocket(
    `wss://api.assemblyai.com/v2/realtime/ws?sample_rate=${SAMPLE_RATE}&disable_partial_transcripts=true`,
    ...,
  );
```

</Tab>
<Tab language="csharp" title="C#">
To disable partial transcripts, set the `disable_partial_transcripts` parameter to `true`.

```csharp {1}
string url = $"wss://api.assemblyai.com/v2/realtime/ws?sample_rate={SAMPLE_RATE}&disable_partial_transcripts=true";
await ws.ConnectAsync(new Uri(url), cts.Token);

```

</Tab>
<Tab language="ruby" title="Ruby">
To disable partial transcripts, set the `disable_partial_transcripts` parameter to `true`.

```ruby {2}
ws = WebSocket::Client::Simple.connect(
  "wss://api.assemblyai.com/v2/realtime/ws?sample_rate=#{SAMPLE_RATE}&disable_partial_transcripts=true",
  ...
)
```

</Tab>
</Tabs>

## Enable extra session information

<Tabs groupId="language">
<Tab language="python-sdk" title="Python SDK" default>
If you enable extra session information, the client receives a `RealtimeSessionInformation` message right before receiving the session termination message.

To enable it, define a callback function to handle the event and configure the `on_extra_session_information` parameter.

```python {2,8}
# Define a callback to handle the session information message
def on_extra_session_information(data: aai.RealtimeSessionInformation):
    print(data.audio_duration_seconds)

# Configure the RealtimeTranscriber
transcriber = aai.RealtimeTranscriber(
    ...,
    on_extra_session_information=on_extra_session_information,
)
```

</Tab>
<Tab language="python" title="Python">
If you enable extra session information, the client receives a `SessionInformation` message right before receiving the session termination message.

To enable it, add the message handler and configure the `on_extra_session_information` parameter.

```python {2,8}
# Define a callback to handle the session information message
if msg_type == 'SessionInformation':
    audio_duration_seconds = msg.get('audio_duration_seconds')
    print(f"Audio duration: {audio_duration_seconds}")

# Configure the on_extra_session_information parameter
ws = websocket.WebSocketApp(
    f'wss://api.assemblyai.com/v2/realtime/ws?sample_rate={SAMPLE_RATE}&enable_extra_session_information=true',
    ...,
)
```

</Tab>
<Tab language="typescript-sdk" title="TypeScript SDK">
The client receives a `SessionInformation` message right before receiving the session termination message.
Handle the `session_information` event to receive the message.

```ts {5}
const rt = client.realtime.transcriber({
  ...
})

rt.on('session_information', (info: SessionInformation) => console.log(info));
```

</Tab>
<Tab language="typescript" title="TypeScript">
If you enable extra session information, the client receives a `SessionInformation` message right before receiving the session termination message.

To enable it, add the message handler and configure the `on_extra_session_information` parameter.

```ts {2,9}
// Define a callback to handle the session information message
if (msgType === 'SessionInformation') {
      const audioDurationSeconds = msg.audio_duration_seconds;
      console.log(`Audio duration: ${audioDurationSeconds}`);
    }

// Configure the on_extra_session_information parameter
const ws = new WebSocket(
    `wss://api.assemblyai.com/v2/realtime/ws?sample_rate=${SAMPLE_RATE}&enable_extra_session_information=true`,
    ...,
  );
```

</Tab>
<Tab language="csharp" title="C#">

If you enable extra session information, the client receives a `SessionInformation` message right before receiving the session termination message.

To enable it, add the message handler and configure the `on_extra_session_information` parameter.

```csharp {2,10}
// Define a callback to handle the session information message
if (messageType == "SessionInformation" && root.TryGetProperty("audio_duration_seconds", out JsonElement audioDurationSecondsElement))
{
    double audioDurationSeconds = audioDurationSecondsElement.GetDouble();
    Console.WriteLine($"Audio duration: {audioDurationSeconds}");
    return;
}

// Configure the on_extra_session_information parameter
string url = $"wss://api.assemblyai.com/v2/realtime/ws?sample_rate={SAMPLE_RATE}&on_extra_session_information=true";
await ws.ConnectAsync(new Uri(url), cts.Token);
```

</Tab>
<Tab language="ruby" title="Ruby">
If you enable extra session information, the client receives a `SessionInformation` message right before receiving the session termination message.

To enable it, add the message handler and configure the `on_extra_session_information` parameter.

```ruby {2,10}
# Define a callback to handle the session information message
if msg_type == 'SessionInformation'
      audio_duration_seconds = msg['audio_duration_seconds']
      puts "Audio duration: #{audio_duration_seconds}"
      return
    end

# Configure the on_extra_session_information parameter
ws = WebSocket::Client::Simple.connect(
  "wss://api.assemblyai.com/v2/realtime/ws?sample_rate=#{SAMPLE_RATE}&on_extra_session_information=true",
  ...
)
```

</Tab>
</Tabs>

## Best practices

Here are some best practices to get the best results from Streaming Speech-to-Text:

### Audio quality

- Use a sample rate of at least 16000 Hz for better transcription accuracy. Higher quality audio input generally leads to better results.
- For noisy environments, see our [Cookbook example](/docs/guides/noise_reduction_streaming) for implementing noise reduction in your streaming pipeline.

### Connection management

- Keep the WebSocket connection open for the entire duration of a session instead of frequently reconnecting. Reconnecting adds latency overhead and can impact real-time performance.
- For client-side implementations of our Streaming API, we recommend first creating a temporary authentication token using a [temporary token](/docs/speech-to-text/streaming#authenticate-with-a-temporary-token) to avoid exposing your API key.

### Optimizing for latency

You can optimize for lower latency in several ways:

- Use partial transcripts instead of waiting for final transcripts. Partial transcripts provide faster feedback but may be less accurate. See our [Cookbook example](/docs/guides/partial_transcripts) for more information.
- [Configure the threshold for silence between speaker turns](/docs/speech-to-text/streaming#configure-the-threshold-for-automatic-utterance-detection) using `end_utterance_silence_threshold` to control when utterances are finalized.
- If you don't need real-time feedback, you can disable partial transcripts to reduce network traffic.

## Learn more

To learn about using Streaming Speech-to-Text, see the following resources:

- [Blog post: Automatically Transcribe Zoom Calls in Real Time](https://www.assemblyai.com/blog/how-to-automatically-transcribe-zoom-calls/)
- [Blog post: Transcribe Twilio Phone Calls](https://www.assemblyai.com/blog/transcribe-twilio-phone-calls-in-real-time-with-assemblyai/)
- [Blog post: Real Time Speech Recognition with Python and PyAudio](https://www.assemblyai.com/blog/real-time-speech-recognition-with-python/)
- [GitHub: End-to-end examples](https://github.com/AssemblyAI-Community/docs-snippets/tree/main/real-time)
- [GitHub: Cookbook examples](https://github.com/AssemblyAI/cookbook/tree/master/streaming-stt)
- [GitHub: Use Express.js for Streaming Speech-to-Text](https://github.com/AssemblyAI/realtime-transcription-browser-js-example)
- [Streaming API reference](https://assemblyai.com/docs/api-reference/streaming)
